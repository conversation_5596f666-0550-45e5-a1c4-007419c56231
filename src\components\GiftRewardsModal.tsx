'use client';

import React from 'react';
import { X, Check, Lock, Gift } from 'lucide-react';

interface TaskReward {
  id: string;
  name: string;
  type: 'currency' | 'item' | 'special';
  icon: string;
  amount?: string;
  completed: boolean;
}

interface GiftRewardsModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'daily' | 'weekly' | 'monthly' | 'standard' | 'pass_cycle' | 'diamond_cycle' | null;
}

const GiftRewardsModal: React.FC<GiftRewardsModalProps> = ({ isOpen, onClose, type }) => {
  if (!isOpen || !type) return null;

  // Mock data for task rewards
  const taskData = {
    daily: {
      title: 'Daily Gifts',
      current: 1,
      total: 15,
      color: 'orange',
      rewards: [
        { id: '1', name: 'Login Bonus', type: 'currency', icon: '🔥', amount: '50', completed: true },
        { id: '2', name: 'First Chat', type: 'currency', icon: '💎', amount: '10', completed: false },
        { id: '3', name: 'Character Creation', type: 'item', icon: '🎁', completed: false },
        { id: '4', name: 'Share Moment', type: 'currency', icon: '🧩', amount: '5', completed: false },
        { id: '5', name: 'Profile Update', type: 'currency', icon: '💧', amount: '20', completed: false },
        { id: '6', name: 'Community Like', type: 'currency', icon: '🔥', amount: '30', completed: false },
        { id: '7', name: 'Daily Streak 3', type: 'item', icon: '🎁', completed: false },
        { id: '8', name: 'Chat 10 Messages', type: 'currency', icon: '💎', amount: '25', completed: false },
        { id: '9', name: 'Discover New Character', type: 'currency', icon: '🧩', amount: '15', completed: false },
        { id: '10', name: 'Daily Streak 7', type: 'special', icon: '⭐', completed: false },
        { id: '11', name: 'Complete Tutorial', type: 'currency', icon: '🔥', amount: '100', completed: false },
        { id: '12', name: 'First Follow', type: 'currency', icon: '💧', amount: '40', completed: false },
        { id: '13', name: 'Daily Streak 14', type: 'item', icon: '🏆', completed: false },
        { id: '14', name: 'Rate Character', type: 'currency', icon: '💎', amount: '35', completed: false },
        { id: '15', name: 'Daily Master', type: 'special', icon: '👑', completed: false },
      ] as TaskReward[]
    },
    weekly: {
      title: 'Weekly Gifts',
      current: 3,
      total: 20,
      color: 'blue',
      rewards: [
        { id: '1', name: 'Weekly Login', type: 'currency', icon: '🔥', amount: '200', completed: true },
        { id: '2', name: 'Chat Streak', type: 'currency', icon: '💎', amount: '50', completed: true },
        { id: '3', name: 'Share Character', type: 'special', icon: '⭐', completed: true },
        { id: '4', name: 'Community Vote', type: 'currency', icon: '🧩', amount: '5', completed: false },
        { id: '5', name: 'Create 3 Characters', type: 'currency', icon: '💧', amount: '100', completed: false },
        { id: '6', name: 'Weekly Explorer', type: 'item', icon: '🎁', completed: false },
        { id: '7', name: 'Social Butterfly', type: 'currency', icon: '🔥', amount: '150', completed: false },
        { id: '8', name: 'Weekly Streak 2', type: 'special', icon: '🌟', completed: false },
        { id: '9', name: 'Help Community', type: 'currency', icon: '💎', amount: '75', completed: false },
        { id: '10', name: 'Weekly Champion', type: 'item', icon: '🏆', completed: false },
        { id: '11', name: 'Content Creator', type: 'currency', icon: '🧩', amount: '200', completed: false },
        { id: '12', name: 'Weekly Streak 4', type: 'special', icon: '💫', completed: false },
        { id: '13', name: 'Mentor Badge', type: 'item', icon: '🎖️', completed: false },
        { id: '14', name: 'Weekly Legend', type: 'currency', icon: '💧', amount: '300', completed: false },
        { id: '15', name: 'Community Leader', type: 'special', icon: '👑', completed: false },
        { id: '16', name: 'Weekly Streak 8', type: 'item', icon: '🏅', completed: false },
        { id: '17', name: 'Innovation Award', type: 'currency', icon: '🔥', amount: '400', completed: false },
        { id: '18', name: 'Weekly Master', type: 'special', icon: '🎭', completed: false },
        { id: '19', name: 'Ultimate Creator', type: 'item', icon: '💎', completed: false },
        { id: '20', name: 'Weekly Grandmaster', type: 'special', icon: '👑', completed: false },
      ] as TaskReward[]
    },
    monthly: {
      title: 'Monthly Gifts',
      current: 5,
      total: 25,
      color: 'purple',
      rewards: [
        { id: '1', name: 'Monthly Champion', type: 'special', icon: '👑', completed: true },
        { id: '2', name: 'Creator Badge', type: 'item', icon: '🏆', completed: true },
        { id: '3', name: 'Premium Bonus', type: 'currency', icon: '💎', amount: '500', completed: true },
        { id: '4', name: 'Exclusive Avatar', type: 'special', icon: '🎭', completed: true },
        { id: '5', name: 'VIP Access', type: 'special', icon: '🌟', completed: true },
        { id: '6', name: 'Monthly Streak 1', type: 'currency', icon: '🔥', amount: '1000', completed: false },
        { id: '7', name: 'Elite Member', type: 'item', icon: '💫', completed: false },
        { id: '8', name: 'Monthly Legend', type: 'special', icon: '⭐', completed: false },
        { id: '9', name: 'Platinum Reward', type: 'currency', icon: '💎', amount: '750', completed: false },
        { id: '10', name: 'Monthly Master', type: 'item', icon: '🏅', completed: false },
        { id: '11', name: 'Diamond Status', type: 'special', icon: '💎', completed: false },
        { id: '12', name: 'Monthly Streak 3', type: 'currency', icon: '🧩', amount: '1500', completed: false },
        { id: '13', name: 'Legendary Badge', type: 'item', icon: '🎖️', completed: false },
        { id: '14', name: 'Monthly Hero', type: 'special', icon: '🦸', completed: false },
        { id: '15', name: 'Ultimate Reward', type: 'currency', icon: '💧', amount: '2000', completed: false },
        { id: '16', name: 'Monthly Streak 6', type: 'item', icon: '🏆', completed: false },
        { id: '17', name: 'Mythical Status', type: 'special', icon: '🔮', completed: false },
        { id: '18', name: 'Monthly Sage', type: 'currency', icon: '🔥', amount: '2500', completed: false },
        { id: '19', name: 'Cosmic Badge', type: 'item', icon: '🌌', completed: false },
        { id: '20', name: 'Monthly Deity', type: 'special', icon: '⚡', completed: false },
        { id: '21', name: 'Infinite Reward', type: 'currency', icon: '💎', amount: '3000', completed: false },
        { id: '22', name: 'Monthly Streak 12', type: 'item', icon: '🎭', completed: false },
        { id: '23', name: 'Transcendent', type: 'special', icon: '🌟', completed: false },
        { id: '24', name: 'Monthly Emperor', type: 'currency', icon: '👑', amount: '5000', completed: false },
        { id: '25', name: 'Monthly Grandmaster', type: 'special', icon: '🏛️', completed: false },
      ] as TaskReward[]
    },
    standard: {
      title: 'Standard Subscription Gifts',
      current: 15,
      total: 25,
      color: 'orange',
      rewards: [
        { id: '1', name: 'Day 1 Login', type: 'currency', icon: '💎', amount: '10', completed: true },
        { id: '2', name: 'Day 3 Bonus', type: 'currency', icon: '✨', amount: '5', completed: true },
        { id: '3', name: 'Day 5 Gift', type: 'item', icon: '🎁', completed: true },
        { id: '4', name: 'Day 7 Reward', type: 'currency', icon: '💎', amount: '20', completed: true },
        { id: '5', name: 'Day 10 Bonus', type: 'currency', icon: '✨', amount: '15', completed: true },
        { id: '6', name: 'Day 12 Item', type: 'item', icon: '🧩', completed: false },
        { id: '7', name: 'Day 15 Reward', type: 'currency', icon: '💎', amount: '30', completed: false },
        { id: '8', name: 'Day 18 Bonus', type: 'currency', icon: '✨', amount: '25', completed: false },
        { id: '9', name: 'Day 20 Gift', type: 'item', icon: '🎁', completed: false },
        { id: '10', name: 'Day 22 Reward', type: 'currency', icon: '💎', amount: '40', completed: false },
        { id: '11', name: 'Day 25 Final', type: 'special', icon: '🏆', completed: false },
      ] as TaskReward[]
    },
    pass_cycle: {
      title: 'Pass Subscription Gifts',
      current: 8,
      total: 25,
      color: 'blue',
      rewards: [
        { id: '1', name: 'Day 1 Pass Login', type: 'currency', icon: '💎', amount: '25', completed: true },
        { id: '2', name: 'Day 2 Pass Bonus', type: 'currency', icon: '✨', amount: '15', completed: true },
        { id: '3', name: 'Day 3 Pass Gift', type: 'item', icon: '🎁', completed: true },
        { id: '4', name: 'Day 5 Pass Reward', type: 'currency', icon: '💎', amount: '50', completed: true },
        { id: '5', name: 'Day 7 Pass Bonus', type: 'currency', icon: '✨', amount: '30', completed: true },
        { id: '6', name: 'Day 8 Pass Item', type: 'item', icon: '🧩', completed: true },
        { id: '7', name: 'Day 10 Pass Reward', type: 'currency', icon: '💎', amount: '75', completed: true },
        { id: '8', name: 'Day 12 Pass Bonus', type: 'currency', icon: '✨', amount: '40', completed: true },
        { id: '9', name: 'Day 14 Pass Gift', type: 'special', icon: '⭐', completed: false },
        { id: '10', name: 'Day 16 Pass Reward', type: 'currency', icon: '💎', amount: '100', completed: false },
        { id: '11', name: 'Day 18 Pass Bonus', type: 'currency', icon: '✨', amount: '60', completed: false },
        { id: '12', name: 'Day 20 Pass Item', type: 'item', icon: '🎁', completed: false },
        { id: '13', name: 'Day 22 Pass Reward', type: 'currency', icon: '💎', amount: '125', completed: false },
        { id: '14', name: 'Day 24 Pass Bonus', type: 'currency', icon: '✨', amount: '80', completed: false },
        { id: '15', name: 'Day 25 Pass Final', type: 'special', icon: '🏆', completed: false },
      ] as TaskReward[]
    },
    diamond_cycle: {
      title: 'Alphane Diamond Subscription Gifts',
      current: 12,
      total: 25,
      color: 'purple',
      rewards: [
        { id: '1', name: 'Day 1 Diamond Login', type: 'currency', icon: '💎', amount: '50', completed: true },
        { id: '2', name: 'Day 2 Diamond Bonus', type: 'currency', icon: '✨', amount: '30', completed: true },
        { id: '3', name: 'Day 3 Diamond Gift', type: 'item', icon: '🎁', completed: true },
        { id: '4', name: 'Day 4 Diamond Reward', type: 'currency', icon: '💎', amount: '75', completed: true },
        { id: '5', name: 'Day 5 Diamond Bonus', type: 'currency', icon: '✨', amount: '50', completed: true },
        { id: '6', name: 'Day 6 Diamond Item', type: 'item', icon: '🧩', completed: true },
        { id: '7', name: 'Day 7 Diamond Reward', type: 'currency', icon: '💎', amount: '100', completed: true },
        { id: '8', name: 'Day 8 Diamond Bonus', type: 'currency', icon: '✨', amount: '70', completed: true },
        { id: '9', name: 'Day 9 Diamond Gift', type: 'special', icon: '👑', completed: true },
        { id: '10', name: 'Day 10 Diamond Reward', type: 'currency', icon: '💎', amount: '150', completed: true },
        { id: '11', name: 'Day 11 Diamond Bonus', type: 'currency', icon: '✨', amount: '90', completed: true },
        { id: '12', name: 'Day 12 Diamond Item', type: 'special', icon: '🌟', completed: true },
        { id: '13', name: 'Day 13 Diamond Reward', type: 'currency', icon: '💎', amount: '200', completed: false },
        { id: '14', name: 'Day 14 Diamond Bonus', type: 'currency', icon: '✨', amount: '120', completed: false },
        { id: '15', name: 'Day 15 Diamond Gift', type: 'item', icon: '🎁', completed: false },
        { id: '16', name: 'Day 16 Diamond Reward', type: 'currency', icon: '💎', amount: '250', completed: false },
        { id: '17', name: 'Day 17 Diamond Bonus', type: 'currency', icon: '✨', amount: '150', completed: false },
        { id: '18', name: 'Day 18 Diamond Item', type: 'special', icon: '💫', completed: false },
        { id: '19', name: 'Day 19 Diamond Reward', type: 'currency', icon: '💎', amount: '300', completed: false },
        { id: '20', name: 'Day 20 Diamond Bonus', type: 'currency', icon: '✨', amount: '200', completed: false },
        { id: '21', name: 'Day 21 Diamond Gift', type: 'item', icon: '🎁', completed: false },
        { id: '22', name: 'Day 22 Diamond Reward', type: 'currency', icon: '💎', amount: '400', completed: false },
        { id: '23', name: 'Day 23 Diamond Bonus', type: 'currency', icon: '✨', amount: '250', completed: false },
        { id: '24', name: 'Day 24 Diamond Item', type: 'special', icon: '👑', completed: false },
        { id: '25', name: 'Day 25 Diamond Final', type: 'special', icon: '🏆', completed: false },
      ] as TaskReward[]
    }
  };

  const data = taskData[type];

  const getTypeColor = (rewardType: string) => {
    switch (rewardType) {
      case 'currency':
        return 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200';
      case 'item':
        return 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200';
      case 'special':
        return 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200';
      default:
        return 'bg-gray-100 dark:bg-gray-900/50 text-gray-800 dark:text-gray-200';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-card border border-border rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden theme-transition">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <Gift className="w-6 h-6 text-primary" />
            <div>
              <h2 className="text-xl font-semibold">{data.title}</h2>
              <p className="text-sm text-foreground/70">
                Progress: {data.current}/{data.total} completed
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="grid gap-3">
            {data.rewards.map((reward, index) => (
              <div
                key={reward.id}
                className={`p-4 border rounded-lg flex items-center justify-between ${
                  reward.completed
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : index < data.current
                    ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
                    : 'border-border opacity-60'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{reward.icon}</div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{reward.name}</span>
                      {reward.amount && (
                        <span className={`px-2 py-1 rounded-full text-xs ${getTypeColor(reward.type)}`}>
                          {reward.amount}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-foreground/50">
                      {type && ['standard', 'pass_cycle', 'diamond_cycle'].includes(type)
                        ? `Login Day ${index + 1} • ${reward.type.charAt(0).toUpperCase() + reward.type.slice(1)} Reward`
                        : `Task #${index + 1} • ${reward.type.charAt(0).toUpperCase() + reward.type.slice(1)} Reward`
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  {reward.completed ? (
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                  ) : index < data.current ? (
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                      <Gift className="w-4 h-4 text-white" />
                    </div>
                  ) : (
                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      <Lock className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GiftRewardsModal;
