'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Co<PERSON>, Sparkles, Heart, RefreshC<PERSON>, Gamepad2, Star, Gem, Flame, Puzzle, Receipt, Search, Filter, Clock, CheckCircle, XCircle, AlertCircle, Calendar, CreditCard, ShoppingBag, Download, Eye, Crown, Zap, Globe, TrendingUp } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import MindFuelDisplay from '@/components/mind-fuel/MindFuelDisplay';
import MembershipUpgradePromo from '@/components/mind-fuel/MembershipUpgradePromo';
import EnhancedTabNavigation, { TabItem } from '@/components/common/EnhancedTabNavigation';
import { getWalletTabColors } from '@/utils/tabColorSchemes';

interface WalletClientPageProps {
  lang: string;
}

interface CurrencyBalance {
  type: 'star_diamonds' | 'joy_crystals' | 'glimmering_dust' | 'memory_puzzles';
  amount: number;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  spent24h: number;
  earned24h: number;
}

interface Order {
  id: string;
  type: 'subscription' | 'currency' | 'character' | 'item';
  title: string;
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
  date: Date;
  paymentMethod: string;
  description: string;
}

interface MindFuelData {
  current: number;
  max: number;
  recoveryTime: number;
  used24h: number;
  earned24h: number;
}

interface MembershipData {
  tier: 'standard' | 'pass' | 'diamond' | 'metaverse';
  name: string;
  expiryDate?: Date;
  features: string[];
  nextTier?: string;
  benefits: {
    mindFuelBonus: string;
    recoverySpeed: string;
    specialFeatures: string[];
  };
}

const WalletClientPage: React.FC<WalletClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [activeTab, setActiveTab] = useState<'overview' | 'orders' | 'membership'>('overview');
  const [orderFilter, setOrderFilter] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Currency data aligned with Store system
  const [currencyBalances] = useState<CurrencyBalance[]>([
    {
      type: 'star_diamonds',
      amount: 2500,
      name: 'Alphane',
      description: t('wallet.alphaneDesc'),
      icon: Sparkles,
      color: 'from-amber-400 to-yellow-500',
      spent24h: 150,
      earned24h: 200
    },
    {
      type: 'joy_crystals',
      amount: 180,
      name: 'Endora',
      description: t('wallet.endoraDesc'),
      icon: Gem,
      color: 'from-blue-400 to-indigo-500',
      spent24h: 25,
      earned24h: 50
    },
    {
      type: 'glimmering_dust',
      amount: 1250,
      name: 'Serotile',
      description: t('wallet.serotileDesc'),
      icon: Puzzle,
      color: 'from-purple-400 to-pink-500',
      spent24h: 80,
      earned24h: 120
    },
    {
      type: 'memory_puzzles',
      amount: 23,
      name: 'Oxytol',
      description: t('wallet.oxytolDesc'),
      icon: Heart,
      color: 'from-rose-400 to-red-500',
      spent24h: 2,
      earned24h: 3
    }
  ]);

  // Mind Fuel data
  const [mindFuelData] = useState<MindFuelData>({
    current: 18,
    max: 20,
    recoveryTime: Date.now() + 25 * 60 * 1000, // 25 minutes
    used24h: 15,
    earned24h: 12
  });

  // Membership data
  const [membershipData] = useState<MembershipData>({
    tier: 'pass',
    name: 'Alphane Pass',
    expiryDate: new Date('2024-02-15'),
    features: ['Double Mind Fuel', 'Priority Support', 'Exclusive Avatars'],
    nextTier: 'Alphane Diamond',
    benefits: {
      mindFuelBonus: '2x Max Capacity',
      recoverySpeed: '2x Faster Recovery',
      specialFeatures: ['Member Badge', 'Priority Queue', 'Exclusive Content']
    }
  });

  // Orders data
  const [orders] = useState<Order[]>([
    {
      id: 'ORD-001',
      type: 'subscription',
      title: 'Alphane Diamond - Monthly',
      amount: 19.99,
      currency: 'USD',
      status: 'completed',
      date: new Date('2024-01-15'),
      paymentMethod: 'Credit Card',
      description: 'Monthly subscription for premium features'
    },
    {
      id: 'ORD-002', 
      type: 'currency',
      title: 'Premium Currency Pack',
      amount: 9.99,
      currency: 'USD',
      status: 'completed',
      date: new Date('2024-01-10'),
      paymentMethod: 'PayPal',
      description: '1,000 Joy Crystals + 500 Glimmering Dust'
    },
    {
      id: 'ORD-003',
      type: 'character',
      title: 'Legendary Character: Aria',
      amount: 14.99,
      currency: 'USD',
      status: 'pending',
      date: new Date('2024-01-20'),
      paymentMethod: 'Credit Card',
      description: 'Exclusive AI character with unique personality'
    },
    {
      id: 'ORD-004',
      type: 'currency',
      title: 'Star Diamonds - Large Pack',
      amount: 49.99,
      currency: 'USD',
      status: 'completed',
      date: new Date('2024-01-05'),
      paymentMethod: 'Apple Pay',
      description: '5,000 Star Diamonds premium currency'
    }
  ]);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 800);
    return () => clearTimeout(timer);
  }, []);

  const handleRefresh = () => {
    setIsLoading(true);
    setLastRefresh(new Date());
    setTimeout(() => setIsLoading(false), 1000);
  };

  const formatLastRefresh = () => {
    return new Intl.DateTimeFormat(lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(lastRefresh);
  };

  // Helper functions now use the currency object properties directly
  const getCurrencyIcon = (currency: CurrencyBalance) => {
    const IconComponent = currency.icon;
    return <IconComponent className="w-6 h-6" />;
  };

  const getCurrencyColor = (currency: CurrencyBalance) => {
    return currency.color;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'pending': return <Clock className="w-5 h-5 text-yellow-600" />;
      case 'failed': return <XCircle className="w-5 h-5 text-red-600" />;
      case 'cancelled': return <AlertCircle className="w-5 h-5 text-gray-600" />;
      default: return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'pass': return Crown;
      case 'diamond': return Gem;
      case 'metaverse': return Globe;
      default: return Heart;
    }
  };

  const getTierGradient = (tier: string) => {
    switch (tier) {
      case 'pass': return 'from-blue-400 to-blue-600';
      case 'diamond': return 'from-purple-400 to-pink-400';
      case 'metaverse': return 'from-indigo-400 via-purple-400 to-pink-400';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesStatus = orderFilter === 'all' || order.status === orderFilter;
    const matchesSearch = order.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         order.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const mockUser = {
    stamina_current: mindFuelData.current,
    stamina_max: mindFuelData.max,
    stamina_recovery_time: mindFuelData.recoveryTime,
    membership_tier: membershipData.tier
  };

  if (isLoading) {
    return (
      <MainAppLayout lang={lang} title={t('wallet.title')}>
        <div className="min-h-screen bg-gradient-to-br from-rose-50 via-purple-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-slate-900 flex items-center justify-center">
          <div className="text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-200 border-t-purple-600 mx-auto mb-4"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Wallet className="w-6 h-6 text-purple-600 animate-pulse" />
              </div>
            </div>
            <p className="text-gray-600 dark:text-gray-300 font-medium">{t('wallet.loading')}</p>
          </div>
        </div>
      </MainAppLayout>
    );
  }

  return (
    <MainAppLayout lang={lang} title={t('wallet.title')}>
      <div className="min-h-screen bg-gradient-to-br from-rose-50 via-purple-50 via-pink-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900/10 dark:to-slate-900">
        {/* Enhanced Header */}
        <div className="relative overflow-hidden">
          {/* Decorative Background */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-500 to-rose-500 opacity-90"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.05),transparent_50%)]"></div>
          
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-white/20 backdrop-blur-md rounded-xl flex items-center justify-center border border-white/30 shadow-xl">
                    <Wallet className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                    <Sparkles className="w-2.5 h-2.5 text-yellow-800" />
                  </div>
                </div>
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold text-white">{t('wallet.title')}</h1>
                  <p className="text-white/80 text-sm">{t('wallet.gameWallet')}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="text-right text-white/90">
                  <div className="text-xs opacity-80">{t('wallet.lastUpdated')}</div>
                  <div className="text-sm font-semibold">{formatLastRefresh()}</div>
                </div>
                <button 
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="p-2 bg-white/20 hover:bg-white/30 backdrop-blur-md rounded-lg transition-all duration-200 disabled:opacity-50 border border-white/20 shadow-lg"
                >
                  <RefreshCw className={`w-4 h-4 text-white ${isLoading ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Tab Navigation */}
        <div className="pt-6">
          <EnhancedTabNavigation
            tabs={[
              { id: 'overview', icon: Coins, label: t('wallet.overview'), description: 'Currency & Mind Fuel' },
              { id: 'orders', icon: Receipt, label: t('wallet.orders'), description: 'Purchase History' },
              { id: 'membership', icon: Crown, label: t('wallet.membership'), description: 'Subscription & Benefits' }
            ]}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            getTabColors={getWalletTabColors}
            containerMaxWidth="max-w-7xl"
          />
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-4">

              {/* Mind Fuel Section - Simplified */}
              <div className="relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 via-pink-500/5 to-rose-500/5 rounded-xl"></div>
                <div className="relative bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50 p-4 lg:p-5">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-4">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="w-14 h-14 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                          <Heart className="w-7 h-7 text-white fill-current" />
                        </div>
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                          <Flame className="w-2.5 h-2.5 text-yellow-800" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">{t('wallet.mindFuel')}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.mindFuelDesc')}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                    <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-3">
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">{mindFuelData.current}/{mindFuelData.max}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.current')}</div>
                    </div>
                    
                    <div className="bg-red-50/50 dark:bg-red-900/20 backdrop-blur-sm rounded-lg p-3">
                      <div className="text-2xl font-bold text-red-600 dark:text-red-400">-{mindFuelData.used24h}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.used24h')}</div>
                    </div>
                    
                    <div className="bg-green-50/50 dark:bg-green-900/20 backdrop-blur-sm rounded-lg p-3">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">+{mindFuelData.earned24h}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.earned24h')}</div>
                    </div>
                    
                    <div className="bg-purple-50/50 dark:bg-purple-900/20 backdrop-blur-sm rounded-lg p-3">
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{Math.ceil((mindFuelData.recoveryTime - Date.now()) / (60 * 1000))}m</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.nextRecovery')}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Currency Cards Grid - Compact */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {currencyBalances.map((currency, index) => (
                  <div
                    key={currency.type}
                    className="group relative overflow-hidden"
                  >
                    <div className={`absolute inset-0 bg-gradient-to-r ${currency.color} opacity-5 rounded-xl`}></div>
                    
                    <div className="relative bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50 p-4 lg:p-5 transition-all duration-300 group-hover:shadow-xl">
                      {/* Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <div className={`w-10 h-10 bg-gradient-to-r ${currency.color} rounded-lg flex items-center justify-center shadow-md text-white`}>
                              {getCurrencyIcon(currency)}
                            </div>
                          </div>
                          <div>
                            <h3 className="text-base font-bold text-gray-900 dark:text-gray-100">
                              {currency.name}
                            </h3>
                            <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-1">
                              {currency.description}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      {/* Balance Display */}
                      <div className="mb-3">
                        <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                          {currency.amount.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{t('wallet.balance')}</div>
                      </div>
                      
                      {/* Stats Grid */}
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-red-50/50 dark:bg-red-900/20 backdrop-blur-sm rounded-lg p-2 text-center">
                          <div className="text-sm font-bold text-red-600 dark:text-red-400">-{currency.spent24h}</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.spent24h')}</div>
                        </div>
                        
                        <div className="bg-green-50/50 dark:bg-green-900/20 backdrop-blur-sm rounded-lg p-2 text-center">
                          <div className="text-sm font-bold text-green-600 dark:text-green-400">+{currency.earned24h}</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.earned24h')}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Quick Actions - Only in Overview Tab */}
              <div className="relative overflow-hidden mt-4">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-600 to-rose-600 rounded-xl"></div>
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(255,255,255,0.1),transparent_70%)] rounded-xl"></div>
                
                <div className="relative p-4 text-white">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-lg flex items-center justify-center">
                      <Sparkles className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="text-base font-bold">{t('wallet.quickActions')}</h3>
                      <p className="text-xs text-white/80">Manage your digital economy</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                    <button
                      onClick={() => window.location.href = `/${lang}/store?category=currency`}
                      className="group bg-white/10 backdrop-blur-md rounded-lg p-3 hover:bg-white/20 transition-all duration-300 text-left border border-white/20"
                    >
                      <Coins className="w-5 h-5 mb-2" />
                      <div className="font-semibold text-sm mb-1">{t('wallet.buyCurrency')}</div>
                      <div className="text-xs text-white/80 line-clamp-1">{t('wallet.buyCurrencyDesc')}</div>
                    </button>
                    
                    <button
                      onClick={() => window.location.href = `/${lang}/store?category=subscription`}
                      className="group bg-white/10 backdrop-blur-md rounded-lg p-3 hover:bg-white/20 transition-all duration-300 text-left border border-white/20"
                    >
                      <Crown className="w-5 h-5 mb-2" />
                      <div className="font-semibold text-sm mb-1">{t('wallet.upgradeMembership')}</div>
                      <div className="text-xs text-white/80 line-clamp-1">{t('wallet.upgradeMembershipDesc')}</div>
                    </button>
                    
                    <button
                      onClick={() => window.location.href = `/${lang}/mind-fuel`}
                      className="group bg-white/10 backdrop-blur-md rounded-lg p-3 hover:bg-white/20 transition-all duration-300 text-left border border-white/20"
                    >
                      <Heart className="w-5 h-5 mb-2" />
                      <div className="font-semibold text-sm mb-1">{t('wallet.manageMindFuel')}</div>
                      <div className="text-xs text-white/80 line-clamp-1">{t('wallet.manageMindFuelDesc')}</div>
                    </button>
                    
                    <button
                      onClick={() => window.location.href = `/${lang}/journey`}
                      className="group bg-white/10 backdrop-blur-md rounded-lg p-3 hover:bg-white/20 transition-all duration-300 text-left border border-white/20"
                    >
                      <TrendingUp className="w-5 h-5 mb-2" />
                      <div className="font-semibold text-sm mb-1">{t('wallet.earnRewards')}</div>
                      <div className="text-xs text-white/80 line-clamp-1">{t('wallet.earnRewardsDesc')}</div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Compact Orders Tab */}
          {activeTab === 'orders' && (
            <div className="space-y-4">
              {/* Compact Search and Filters */}
              <div className="bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50 p-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder={t('wallet.searchOrders')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-9 pr-3 py-2 text-sm bg-white/60 dark:bg-gray-800/60 backdrop-blur-md rounded-lg border border-white/20 dark:border-gray-700/50 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                    />
                  </div>
                  
                  <select
                    value={orderFilter}
                    onChange={(e) => setOrderFilter(e.target.value as any)}
                    className="px-3 py-2 text-sm bg-white/60 dark:bg-gray-800/60 backdrop-blur-md rounded-lg border border-white/20 dark:border-gray-700/50 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 min-w-[140px]"
                  >
                    <option value="all">{t('wallet.allOrders')}</option>
                    <option value="completed">{t('wallet.completed')}</option>
                    <option value="pending">{t('wallet.pending')}</option>
                    <option value="failed">{t('wallet.failed')}</option>
                  </select>
                </div>
              </div>

              {/* Compact Orders List */}
              <div className="space-y-3">
                {filteredOrders.length === 0 ? (
                  <div className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50 p-8 text-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <Receipt className="w-6 h-6 text-gray-400" />
                    </div>
                    <h3 className="text-base font-bold text-gray-900 dark:text-gray-100 mb-1">{t('wallet.noOrders')}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{t('wallet.noOrdersDescription')}</p>
                    <button
                      onClick={() => window.location.href = `/${lang}/store`}
                      className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm font-medium rounded-lg hover:scale-105 transition-all duration-300 shadow-md"
                    >
                      Visit Store
                    </button>
                  </div>
                ) : (
                  filteredOrders.map((order, index) => (
                    <div key={order.id} className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50 p-4 hover:shadow-xl transition-all duration-300">
                      <div className="flex flex-col sm:flex-row sm:items-start gap-3">
                        <div className="flex items-start gap-3 flex-1">
                          {/* Compact Order Icon */}
                          <div className="relative flex-shrink-0">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center shadow-md ${
                              order.type === 'subscription' ? 'bg-gradient-to-br from-purple-500 to-indigo-600' :
                              order.type === 'currency' ? 'bg-gradient-to-br from-yellow-500 to-orange-600' :
                              order.type === 'character' ? 'bg-gradient-to-br from-pink-500 to-rose-600' :
                              'bg-gradient-to-br from-blue-500 to-cyan-600'
                            }`}>
                              {order.type === 'subscription' && <Crown className="w-5 h-5 text-white" />}
                              {order.type === 'currency' && <Coins className="w-5 h-5 text-white" />}
                              {order.type === 'character' && <Heart className="w-5 h-5 text-white" />}
                              {order.type === 'item' && <ShoppingBag className="w-5 h-5 text-white" />}
                            </div>
                            
                            {/* Status Dot */}
                            <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                              order.status === 'completed' ? 'bg-green-500' :
                              order.status === 'pending' ? 'bg-yellow-500' :
                              order.status === 'failed' ? 'bg-red-500' :
                              'bg-gray-500'
                            }`}></div>
                          </div>
                          
                          {/* Order Details */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-2 mb-1">
                              <h3 className="text-sm font-bold text-gray-900 dark:text-gray-100 line-clamp-1">{order.title}</h3>
                              <span className={`inline-flex px-2 py-0.5 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                                {t(`wallet.${order.status}`)}
                              </span>
                            </div>
                            
                            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-1">{order.description}</p>
                            
                            {/* Order Meta */}
                            <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                              <div className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                <span>{order.date.toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <CreditCard className="w-3 h-3" />
                                <span>{order.paymentMethod}</span>
                              </div>
                              <span className="text-xs font-mono">{order.id}</span>
                            </div>
                          </div>
                        </div>
                        
                        {/* Price and Actions */}
                        <div className="flex flex-row sm:flex-col items-center sm:items-end gap-2 mt-2 sm:mt-0">
                          <div className="text-right">
                            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                              ${order.amount.toFixed(2)}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{order.currency}</div>
                          </div>
                          
                          {/* Action Buttons */}
                          <div className="flex gap-1">
                            <button className="p-1.5 bg-white/50 dark:bg-gray-800/50 backdrop-blur-md text-gray-600 dark:text-gray-400 rounded-lg border border-white/20 dark:border-gray-700/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all duration-300">
                              <Eye className="w-3.5 h-3.5" />
                            </button>
                            <button className="p-1.5 bg-white/50 dark:bg-gray-800/50 backdrop-blur-md text-gray-600 dark:text-gray-400 rounded-lg border border-white/20 dark:border-gray-700/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all duration-300">
                              <Download className="w-3.5 h-3.5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {/* Compact Membership Tab */}
          {activeTab === 'membership' && (
            <div className="space-y-4">
              {/* Compact Current Membership */}
              <div className="relative overflow-hidden">
                <div className={`absolute inset-0 bg-gradient-to-r ${getTierGradient(membershipData.tier)} opacity-5 rounded-xl`}></div>
                <div className="relative bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50 p-4">
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-3">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className={`w-10 h-10 bg-gradient-to-r ${getTierGradient(membershipData.tier)} rounded-lg flex items-center justify-center shadow-lg`}>
                          {React.createElement(getTierIcon(membershipData.tier), { className: 'w-5 h-5 text-white' })}
                        </div>
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                          <Crown className="w-2 h-2 text-yellow-800" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-base font-bold text-gray-900 dark:text-gray-100">{membershipData.name}</h3>
                        <p className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.currentMembership')}</p>
                      </div>
                    </div>
                    
                    {membershipData.expiryDate && (
                      <div className="bg-orange-50/50 dark:bg-orange-900/20 backdrop-blur-md rounded-lg px-2 py-1.5 text-center">
                        <div className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.expiresOn')}</div>
                        <div className="text-sm font-bold text-gray-900 dark:text-gray-100">
                          {membershipData.expiryDate.toLocaleDateString()}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Benefits Grid */}
                  <div className="grid grid-cols-3 gap-2 mb-3">
                    <div className="bg-blue-50/50 dark:bg-blue-900/20 backdrop-blur-md rounded-lg p-2 text-center">
                      <div className="text-sm font-bold text-blue-600 dark:text-blue-400">{membershipData.benefits.mindFuelBonus}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.mindFuelBonus')}</div>
                    </div>
                    
                    <div className="bg-green-50/50 dark:bg-green-900/20 backdrop-blur-md rounded-lg p-2 text-center">
                      <div className="text-sm font-bold text-green-600 dark:text-green-400">{membershipData.benefits.recoverySpeed}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.recoverySpeed')}</div>
                    </div>
                    
                    <div className="bg-purple-50/50 dark:bg-purple-900/20 backdrop-blur-md rounded-lg p-2 text-center">
                      <div className="text-sm font-bold text-purple-600 dark:text-purple-400">{membershipData.benefits.specialFeatures.length}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.specialFeatures')}</div>
                    </div>
                  </div>

                  {/* Features List */}
                  <div>
                    <h4 className="text-sm font-bold text-gray-900 dark:text-gray-100 mb-2">{t('wallet.membershipFeatures')}</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-1.5">
                      {membershipData.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2 p-1.5 bg-white/50 dark:bg-gray-800/50 backdrop-blur-md rounded-lg border border-white/20 dark:border-gray-700/50">
                          <div className="w-5 h-5 bg-gradient-to-br from-yellow-400 to-orange-500 rounded flex items-center justify-center flex-shrink-0">
                            <Star className="w-2.5 h-2.5 text-white" />
                          </div>
                          <span className="text-xs text-gray-700 dark:text-gray-300">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Compact Upgrade Section */}
              {membershipData.tier !== 'metaverse' && (
                <div className="bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50 p-3">
                  <div className="text-center">
                    <h4 className="text-sm font-bold text-gray-900 dark:text-gray-100 mb-2">Upgrade to {membershipData.nextTier}</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">Unlock premium features and benefits</p>
                    <button
                      onClick={() => window.location.href = `/${lang}/store?category=subscription`}
                      className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm font-medium rounded-lg hover:scale-105 transition-all duration-300 shadow-md"
                    >
                      Upgrade Now
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default WalletClientPage; 