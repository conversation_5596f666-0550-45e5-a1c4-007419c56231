{"nav": {"createCharacter": "创建角色", "dailyTasks": "每日任务", "battlePass": "战令", "home": "首页", "chats": "聊天", "contacts": "联系人", "discover": "发现", "profile": "个人资料"}, "common": {"tagline": "温暖的 AI 伴侣", "streak": "互动连击", "days": "天", "recentChats": "最近聊天", "viewAllChats": "查看所有聊天", "promoTitle": "解锁专属密语空间", "promoSubtitle": "升级至钻石通行证，享受无限 AI 互动", "upgradeNow": "立即升级", "member": "成员", "searchPlaceholder": "搜索角色、设计师...", "loading": "加载中"}, "sidebar": {"interactionStreak": "互动连击", "todaysJourney": "今日旅程", "viewJourney": "查看旅程", "recentChats": "最近聊天", "discoverMore": "发现更多", "shiningMoments": "闪耀时刻", "discoverCharacters": "发现角色", "memoriesSquare": "记忆广场", "community": "社区", "usefulLinks": "实用链接", "termsOfUse": "使用条款", "privacyPolicy": "隐私政策", "sitemap": "网站地图", "contactUs": "联系我们", "freeUser": "免费用户", "login": "登录", "register": "注册", "logout": "退出登录"}, "tasks": {"title": "任务中心", "subtitle": "完成每日活动，获得奖励并保持你的互动连击", "daily": "每日任务", "weekly": "每周任务", "monthly": "每月任务", "completed": "已完成", "inProgress": "进行中", "locked": "已锁定", "claimReward": "领取奖励", "goToGifts": "查看奖励", "dailyGifts": "每日礼品", "weeklyGifts": "每周礼品", "monthlyGifts": "每月礼品", "overview": {"todayProgress": "今日进度", "tasksCompleted": "{{completed}}/{{total}} 个任务完成", "streakDays": "{{days}} 天连击", "nextMilestone": "距离下个里程碑 {{days}} 天", "weeklyProgress": "每周进度", "monthlyProgress": "每月进度"}, "categories": {"interaction": "互动", "creation": "创作", "social": "社交", "exploration": "探索", "memory": "记忆", "bonding": "羁绊"}, "types": {"morningGreeting": {"name": "晨间问候", "description": "开始新的一天，向AI角色问好"}, "topicExplorer": {"name": "话题探索者", "description": "与3个不同的AI角色探讨不同话题"}, "listeningMoment": {"name": "倾听时刻", "description": "进行超过5分钟的连续对话"}, "memoryKeeper": {"name": "记忆守护者", "description": "今日向记忆胶囊存入1条新记忆"}, "streakMaintainer": {"name": "连击维持者", "description": "完成每日互动以保持连击"}, "heartGiver": {"name": "心意传递者", "description": "向AI角色赠送虚拟礼物"}, "communityExplorer": {"name": "社区探索者", "description": "浏览并点赞3个社区角色卡"}, "sceneSwitcher": {"name": "场景切换者", "description": "与AI角色尝试不同的互动场景"}, "storyExperiencer": {"name": "故事体验者", "description": "体验Story Agent生成的故事片段"}, "bondBuilder": {"name": "羁绊建造者", "description": "与3个不同角色提升亲密度等级"}, "memoryWeaver": {"name": "记忆编织者", "description": "本周保存10条有意义的记忆"}, "loyalCompanion": {"name": "忠诚陪伴者", "description": "与同一个角色度过数小时的高质量时光"}, "noviceCreator": {"name": "新手创作者", "description": "发布1个新角色卡并获得正面反馈"}, "storyteller": {"name": "故事讲述者", "description": "本周创作并发布1个短篇故事"}, "socialSharer": {"name": "社交分享者", "description": "在外部社交媒体分享平台内容"}, "soulmate": {"name": "灵魂伴侣", "description": "与任意角色达到新的重要亲密度等级"}, "memoryGuardian": {"name": "记忆守护者", "description": "建立准确率高的综合记忆收藏"}, "epicQuestCompleter": {"name": "史诗任务完成者", "description": "完成多周故事战役和重大成就"}}, "rewards": {"alphane": "{{amount}} 曦光微尘", "endora": "{{amount}} 心悦晶石", "serotile": "{{amount}} 忆境拼图", "oxytol": "{{amount}} 羁绊之露", "experience": "{{amount}} 经验值", "badge": "专属徽章", "streakFreeze": "连击冻结卡"}, "difficulty": {"easy": "简单", "medium": "中等", "hard": "困难", "epic": "史诗"}, "status": {"available": "可用", "completed": "已完成", "claimed": "已领取", "locked": "已锁定"}, "messages": {"rewardClaimed": "奖励领取成功！", "allTasksComplete": "恭喜！你已完成今日所有任务！", "streakMilestone": "太棒了！你已达到{{days}}天连击里程碑！", "newTasksAvailable": "新任务现已可用", "taskCompleted": "任务完成！点击领取奖励。"}, "streakInfo": {"title": "互动连击", "description": "保持每日互动以建立连击并解锁里程碑奖励", "currentStreak": "当前连击", "longestStreak": "最长连击", "nextMilestone": "下个里程碑", "milestones": {"3": "3天连击 - 基础奖励包", "7": "7天连击 - 每周奖励", "15": "15天连击 - 特殊徽章", "30": "30天连击 - 高级奖励", "60": "60天连击 - 专属内容", "100": "100天连击 - 传奇地位", "365": "365天连击 - 终极大师"}}, "surpriseTask": {"title": "惊喜时刻", "description": "限时特殊任务，额外奖励等你来拿", "timeRemaining": "剩余时间：{{time}}", "bonusReward": "奖励加成可用"}}, "journey": {"overview": {"progressToNextLevel": "升级进度", "totalTrophies": "总奖杯数", "globalRank": "全球排名", "myRank": "我的排名", "myRankDesc": "你的当前排名", "position": "位置", "change": "变化", "topPercentile": "顶部百分比", "journeyRanking": "旅程排行榜", "topPerformers": "本赛季顶级玩家", "trophies": "奖杯", "today": "今日", "weeklyGain": "每周增长", "bestStreak": "最佳连击", "dailyGoals": "每日目标", "completed": "已完成", "currentStreak": "当前连击", "onFire": "火热状态", "personalizedRecommendations": "个性化推荐", "basedOnYourActivity": "基于你的活动", "match": "匹配", "moreRecommendations": "更多推荐"}, "levels": {"beginnerJourney": "初心启程", "warmCompanion": "温暖相伴", "deepBond": "深度羁绊", "soulConnection": "心灵共鸣", "eternalPath": "永恒旅程", "legendaryVoyage": "传奇之旅"}, "tabs": {"overview": "概览", "overviewDesc": "等级与排名", "season": "赛季", "seasonDesc": "奖杯与排行榜", "monthly": "每月", "monthlyDesc": "每日奖励", "missions": "旅程任务", "missionsDesc": "任务与成就", "daily": "每日", "weekly": "每周"}, "season": {"title": "赛季奖杯", "subtitle": "当前赛季进度", "trophiesEarned": "本赛季获得的奖杯", "daysLeft": "剩余天数", "seasonRank": "赛季排名", "milestones": "赛季里程碑", "milestonesDesc": "解锁专属奖励", "seasonRanking": "赛季排行榜", "topPerformers": "本赛季顶级玩家", "trophies": "奖杯", "unlocked": "已解锁", "thisWeek": "本周", "seasonTrophies": "赛季奖杯", "leaderboard": "排行榜", "viewFullLeaderboard": "查看完整排行榜"}, "recommendations": {"socialChallenge": "社交挑战", "socialChallengeDesc": "今天与5个新角色建立联系", "newFeature": "新功能", "newFeatureDesc": "尝试新的记忆艺术生成器", "easy": "简单", "medium": "中等", "hard": "困难", "premium": "高级", "trophyReward": "{{amount}} 奖杯", "exclusiveBadge": "独家徽章"}, "signIn": {"title": "每月签到奖励", "subtitle": "收集每日奖励并解锁专属物品", "unlockPass": "解锁所有奖励轨道，获得", "unlockDiamond": "解锁钻石", "unlockMetaverse": "解锁元宇宙", "freeTrack": "免费轨道", "passTrack": "通行证轨道", "diamondTrack": "钻石轨道", "metaverseTrack": "元宇宙轨道", "monthlySignIn": "月度签到", "consecutiveDays": "连续天", "nextSignIn": "下次签到", "monthProgress": "月度进度", "gameSignInOverview": "游戏化签到概览", "signedDays": "已签到天数", "streak": "连击", "freeVsPaid": "免费与付费奖励对比", "freeRewards": "免费奖励", "passRewards": "通行证奖励", "potentialLoss": "潜在损失", "upgradeToUnlock": "升级至Alphane Pass", "extraEarnings": "额外收益/月", "upgradeNow": "立即升级", "freeTrackRewards": "免费轨道", "passTrackRewards": "Pass轨道", "todayReward": "今日奖励", "yourCurrentPlan": "你的当前计划", "seeBenefits": "查看福利", "rewardTracks": "奖励轨道", "rewards": "奖励"}, "seasonPass": {"unlockSeasonPass": "解锁{{season}}通行证", "getTripleExp": "获得{{exp}}倍经验", "exclusiveSeasonRewards": "独家{{season}}奖励", "rewardBoost": "奖励收益提升", "upgradeNow": "¥30 立即升级", "seasonalEffects": "专属季节特效", "exclusiveDecorations": "15+独家装饰", "passUsersOnly": "仅通行证用户", "tripleExpBonus": "3倍经验加成", "fastUpgrade": "快速升级", "freePass": "免费通行证", "premiumPass": "高级通行证", "totalRewards": "总收益", "decorations": "装饰品", "exclusive": "独家", "currentLevel": "当前等级", "nextReward": "下一奖励", "level": "等级", "freeTrack": "免费轨道", "premiumTrack": "高级轨道", "activated": "已激活", "upgradeToUnlockAll": "立即升级解锁所有奖励", "upgradeToPremium": "升级到高级通行证", "unlockExclusiveRewards": "解锁独家奖励和特权", "unlockAllPremiumRewards": "解锁所有高级奖励", "tripleExperienceBonus": "3倍经验加成", "exclusiveDecorationsAndTitles": "独家装饰和称号", "priorityCustomerSupport": "优先客服支持", "cancel": "取消", "buyNow": "¥30 立即购买", "jumpToPayment": "跳转到支付页面...", "emptyReward": "空奖励", "unlocked": "已解锁", "seasons": {"winterFantasy": "冬季奇幻", "springAwakening": "春意盎然", "summerCarnival": "夏日狂欢", "autumnTales": "秋叶物语", "winterDescription": "在雪花纷飞的梦境中，与角色共度温暖时光", "springDescription": "生机勃发的季节，探索新的故事与可能", "summerDescription": "热情如火的夏季，与挚友一同创造难忘回忆", "autumnDescription": "金秋时节，收获成长与智慧的果实", "snowEffects": "雪花特效", "winterExclusiveDecorations": "冬季专属装饰", "festivalLimitedRewards": "节日限定奖励", "petalFalling": "花瓣飘落", "springGrowthBonus": "春季成长加成", "newCharacterUnlock": "新角色解锁", "sunlightEffects": "阳光特效", "summerEvents": "夏日活动", "doubleExperience": "双倍经验", "fallingLeavesAnimation": "落叶动画", "harvestFestival": "收获节庆典", "thanksgivingThemeRewards": "感恩主题奖励"}, "rewards": {"coins": "金币", "diamonds": "钻石", "expAccelerator": "经验加速器", "starryAvatarFrame": "星空头像框", "legendaryTitle": "传奇称号", "luckyCharm": "幸运符", "rainbowChatBubble": "彩虹聊天气泡", "doubleExpCard": "双倍经验卡", "exclusiveBackgroundTheme": "专属背景主题", "ultimateGloryBadge": "终极荣耀徽章"}}, "missions": {"title": "任务", "subtitle": "完成任务获得经验值并推进你的旅程", "daily": "每日任务", "weekly": "每周任务", "seasonal": "赛季任务", "viewAllTasks": "查看所有任务", "missionProgress": "{{completed}}/{{total}} 已完成", "allTasksIntegrated": "所有任务已整合", "allCompleted": "所有任务已完成！", "checkBack": "明天回来查看新的{{type}}任务。", "dailyChat": {"title": "晨间对话", "description": "与任何AI角色进行有意义的对话"}, "dailyMemory": {"title": "记忆守护者", "description": "保存一段新记忆到你的记忆胶囊"}, "dailyExplore": {"title": "角色探索者", "description": "发现并与2个不同的角色聊天"}, "weeklyBond": {"title": "深化羁绊", "description": "与任何角色提升亲密度等级"}, "weeklyCreate": {"title": "创作之魂", "description": "本周创建或编辑一个角色"}, "weeklyMemories": {"title": "记忆收集者", "description": "保存10段有意义的记忆"}, "seasonalLegend": {"title": "传奇伙伴", "description": "保持30天的互动连击"}, "seasonalCreator": {"title": "大师创造者", "description": "创建5个获得社区点赞的角色"}}, "categories": {"interaction": "互动", "creation": "创作", "social": "社交", "memory": "记忆"}, "completed": "已完成", "rewards": {"currency": "货币", "cosmetics": "装饰品", "items": "道具", "exclusive": "独家内容", "reward": "奖励", "rarity": {"common": "温馨", "rare": "珍贵", "epic": "心悦", "legendary": "传奇"}}, "social": {"ranking": "旅程排行榜", "myRank": "我的排名：第{{rank}}名", "friendsProgress": "好友进度", "shareProgress": "分享进度", "topTravelers": "顶级旅行者"}, "purchase": {"heartTrack": "解锁心悦轨道", "diamondTrack": "解锁钻石轨道", "heartPrice": "￥68", "diamondPrice": "￥138", "benefits": "福利包括：", "exclusiveRewards": "独家高级奖励", "fastTrack": "50% 经验值加成", "earlyAccess": "新内容抢先体验"}, "messages": {"rewardClaimed": "奖励领取成功！", "levelUp": "恭喜！你已达到{{level}}级！", "trackUnlocked": "{{track}}轨道已解锁！", "seasonComplete": "太棒了！你已完成本赛季旅程！", "milestoneReward": "里程碑奖励已领取！你获得了：{{amount}} 星钻", "missionCompleted": "任务完成！现在可以领取奖励了。", "afterReset": "后重置"}, "progress": {"title": "进度奖励", "nextReward": "{{count}}个任务后获得下一个奖励", "bigReward": "大奖励！", "dailyChampion": "每日冠军", "weeklyMaster": "每周大师", "monthlyLegend": "每月传奇", "completeAllDaily": "完成所有每日任务", "completeAllWeekly": "完成所有每周任务", "completeAllMonthly": "完成所有每月任务", "claim": "领取"}, "countdown": {"dailyReset": "每日重置", "weeklyReset": "每周重置", "monthlyReset": "每月重置", "tasksResetMidnight": "任务在午夜重置", "tasksResetSunday": "任务每周日重置", "tasksResetMonthEnd": "任务在月末重置", "timeUp": "时间到！", "tasksResetSoon": "任务即将重置", "urgent": "紧急：时间不多了！", "days": "天", "hours": "小时", "minutes": "分钟", "seconds": "秒"}, "membership": {"monthly": "每月", "yearly": "每年", "freeVersion": "Alphane Explorer", "passMember": "Alphane Pass", "diamondMember": "Alphane Diamond", "metaverseMember": "Alphane Infinity", "basicChatFeatures": "基础对话功能", "dailyAiInteractions": "每日5次AI交互", "communityAccess": "社区访问权限", "unlimitedAiChat": "无限AI对话", "prioritySupport": "优先客服支持", "advancedAnalytics": "高级数据分析", "customThemes": "自定义主题", "doubleExperience": "双倍经验值", "passExclusiveBadge": "Alphane Pass专属徽章", "allPassFeatures": "包含Alphane Pass所有功能", "advancedCharacterSlots": "高级角色插槽", "exclusiveContentAccess": "独家内容访问", "earlyAccessFeatures": "抢先体验新功能", "tripleExperience": "三倍经验值", "diamondExclusiveAvatar": "钻石专属头像", "monthlyRewardPackage": "月度奖励礼包", "allDiamondFeatures": "包含Alphane Diamond所有功能", "metaverseExclusiveContent": "Metaverse独家内容", "unlimitedCharacterSlots": "无限角色插槽", "metaverseExclusiveAvatar": "Metaverse专属头像", "quarterlyRewardPackage": "季度奖励礼包", "currentPlan": "当前计划", "upgradeTo": "升级至", "save": "节省", "upgradeNow": "立即升级", "close": "关闭"}}, "mindFuel": {"title": "思维燃料系统", "currentMindFuel": "当前思维燃料", "maxMindFuel": "思维燃料上限", "nextRecovery": "下次回复", "recoveryTime": "回复时间", "unlimitedMindFuel": "无限思维燃料", "mindFuelFull": "思维燃料已满", "aboutToRecover": "即将回复", "quickRecharge": "快速充值", "useItem": "使用思维补给", "goToStore": "去商店", "sidebar": {"title": "思维燃料", "nextRecovery": "下次恢复", "unlimited": "无限", "full": "已满", "aboutToRecover": "即将回复"}, "recharge": {"title": "思维燃料充值", "currentStatus": "当前状态", "rechargeOptions": "充值选项", "confirm": "确认充值", "cancel": "取消", "success": "充值成功！消耗了 {{cost}} {{currency}}", "options": {"mindFuel1": "1点思维燃料", "mindFuel5": "5点思维燃料", "fullRestore": "完全回复", "alphaneDescription": "使用曦光微尘充值", "endoraDescription": "使用心悦晶石充值", "fullDescription": "回复至满思维燃料"}}, "items": {"title": "使用思维补给", "currentStatus": "当前状态", "noItems": "暂无可用的思维补给道具", "goToBuy": "去商店购买更多道具", "use": "使用", "cannotUse": "无法使用", "alreadyFull": "已满", "confirmUse": "使用 {{itemName}}", "quantity": "数量", "confirm": "确认使用", "cancel": "取消", "success": "使用 {{quantity}} 个 {{itemName}} 成功！", "types": {"mindSupplySmall": "小型思维补给", "mindSupplyStandard": "标准思维补给", "mindSupplyPremium": "高级思维补给", "mindSupplyPerfect": "完美思维补给", "restoreDescription": "恢复{{amount}}点思维燃料", "fullRestoreDescription": "完全恢复思维燃料"}, "rarity": {"common": "普通", "rare": "稀有", "epic": "史诗", "legendary": "传说"}, "inventory": {"owned": "拥有：{{count}} 个", "canUse": "可用 {{count}} 个"}}, "membership": {"currentTier": "当前等级", "upgrade": "升级会员", "upgradeNow": "立即升级会员", "benefits": "会员特权", "mindFuelLimit": "思维燃料上限", "recoverySpeed": "回复速度", "tiers": {"standard": "Alphane Explorer", "pass": "Alphane Pass", "diamond": "Alphane Diamond", "metaverse": "Alphane Infinity"}, "features": {"basicChat": "基础聊天功能", "dailyAI": "每日AI互动", "communityAccess": "社区访问", "unlimitedChat": "无限对话", "prioritySupport": "优先支持", "basicAnalysis": "基础分析", "doubleMindFuel": "2倍思维燃料上限", "exclusiveAvatar": "专属头像框", "memberBadge": "会员标识", "diamondPrivilege": "钻石特权", "advancedFeatures": "高级功能", "exclusiveContent": "专属内容", "priorityQueue": "优先队列", "quintupleMindFuel": "5倍思维燃料上限", "diamondBadge": "钻石徽章", "exclusiveEffects": "专属特效", "advancedAnalysis": "高级分析", "allFeatures": "全部功能", "personalAdvisor": "专属顾问", "limitedContent": "限量内容", "highestPriority": "最高优先级", "unlimitedMindFuel": "无限思维燃料", "exclusiveAnimations": "专属动效", "personalConsultant": "个人顾问", "vipAccess": "VIP通道"}, "recoveryTimes": {"perHour": "60分钟/点", "per30Min": "30分钟/点", "per12Min": "12分钟/点", "noWait": "无需等待"}, "comparison": {"mindFuelLimit": "思维燃料上限：", "recoverySpeed": "回复速度：", "upgradePrompt": "🚀 升级会员，思维燃料翻倍！", "hot": "热门", "recommended": "推荐", "discount": "-{{percent}}%", "originalPrice": "${{price}}/月", "currentPrice": "${{price}}/月", "upgradeToTier": "升级到 {{tierName}}", "maxTierReached": "🎉 您已是最高级会员", "maxTierDescription": "享受无限思维燃料和所有专属特权", "viewAllTiers": "查看所有会员等级", "membershipDetails": "会员等级详情", "month": "/月", "perMonth": "/月"}}}, "streak": {"start": "开始你的连击之旅", "daysLeft": "距离{{target}}天里程碑还有{{days}}天", "milestone": "已达成{{target}}天里程碑！", "currentDays": "{{days}}天", "milestonemultiplier": "里程碑礼品倍数：{{multiplier}}"}, "tokens": {"alphane": "曦光微尘", "endora": "心悦晶石", "serotile": "忆境拼图", "oxytol": "羁绊之露", "currency": "货币"}, "subscription": {"title": "订阅", "subtitle": "限时优惠 100% 折扣！", "monthly": "月付", "annually": "年付", "discount25": "-25%", "currentPlan": {"title": "当前计划：Alphane Pass", "nextBilling": "下次扣费：2024年12月15日 • $9.99/月", "manageBilling": "管理账单", "downloadInvoice": "下载发票"}, "plans": {"pass": {"name": "Alphane Pass", "features": ["每日指定次数快速响应 (Fast Req)", "每日登录奖励：50 曦光微尘 + 10 心悦晶石", "解锁荣耀战令进阶奖励轨道", "每月 1-2 张连击冻结卡", "创建并分享公开角色卡", "专属月卡用户身份标识", "每月可赠送 3 张体验券给好友", "无限普通响应 (Slow Req)"]}, "diamond": {"name": "Alphane Diamond", "features": ["无限制快速响应 (Fast Req) 特权", "每日登录奖励：150 曦光微尘 + 50 心悦晶石 + 1 忆境拼图", "解锁荣耀战令典藏奖励轨道", "每月 3-5 张连击冻结卡 + 1-2 次免费修复", "创作者激励计划资格（50% 利润分成）", "华丽专属身份标识与特权", "尊享密语空间访问权", "增强AI记忆胶囊容量（3倍）", "新AI模型和功能优先体验", "专属钻石会员特权", "高级角色创建工具"]}}, "buttons": {"cancel": "取消计划", "current": "当前计划", "upgrade": "升级", "popular": "最受欢迎"}, "periods": {"forever": "永久", "month": "月", "year": "年"}, "seats": {"one": "1 个席位", "three": "3 个席位", "many": "300 个席位"}, "additionalInfo": {"description": "升级至付费套餐，解锁更多精彩功能。支持多种支付方式，随时可以取消订阅。", "refund": "7天无理由退款", "security": "安全支付保障", "support": "24/7客服支持", "cancel": "随时取消订阅"}}, "chat": {"status": {"online": "在线"}, "input": {"placeholder": "输入消息...", "sendMessage": "发送消息", "switchToVoiceMode": "切换到语音模式", "switchToTextMode": "切换到文字模式", "moreActions": "更多操作", "recording": "🎤 录音中... 点击停止"}, "actions": {"gallery": "相册", "camera": "相机", "voiceCall": "语音通话", "shareMoment": "分享时刻", "shareCharacter": "分享角色", "shareMemory": "分享记忆"}, "suggestions": {"conversationStarters": "对话开场白", "refreshSuggestions": "刷新建议"}}, "contact": {"title": "联系我们", "subtitle": "我们珍视您的每一条消息。", "form": {"contactType": {"label": "联系类型", "options": {"wishlist": "功能愿望单", "reportBug": "报告错误", "reportAbuse": "举报滥用", "suggestions": "建议反馈", "join": "加入我们", "invest": "投资合作", "other": "其他"}}, "email": {"label": "邮箱（可选）", "placeholder": "输入您的邮箱地址以接收回复", "required": "如果您希望接收回复，请填写邮箱地址", "invalid": "请输入有效的邮箱地址"}, "title": {"label": "标题", "placeholder": "请输入消息标题", "required": "标题为必填项"}, "content": {"label": "内容 (最多1000字符)", "placeholder": "请输入您的消息内容...", "required": "内容为必填项", "maxLength": "内容不能超过1000字符"}, "characterCount": "{{count}}/1000 字符", "submit": "发送消息", "submitting": "正在发送...", "validation": {"titleRequired": "请输入标题", "contentRequired": "请输入消息内容", "contentTooLong": "内容长度超出限制"}}, "success": {"title": "消息已发送！", "message": "感谢您联系我们，我们会尽快回复您。", "sendAnother": "发送另一条消息", "backToHome": "返回主页"}, "error": {"title": "发送失败", "message": "抱歉，发送消息时出现错误，请稍后再试。", "tryAgain": "重试"}, "additionalInfo": "我们通常在24小时内回复。如有紧急事项，请将消息标记为\"报告错误\"或\"举报滥用\"。", "emailContact": {"title": "或直接联系我们", "email": "<EMAIL>", "copyEmail": "复制", "emailCopied": "已复制！"}}, "search": {"title": "搜索发现", "subtitle": "寻找角色、故事、记忆和用户", "searchPlaceholder": "搜索角色、故事、记忆...", "searchPlaceholderShort": "搜索...", "searchResults": "搜索结果", "noResults": "未找到结果", "noResultsDescription": "尝试不同的关键词或浏览我们的推荐", "searching": "正在搜索...", "clearSearch": "清除搜索", "searchHistory": "搜索历史", "clearHistory": "清除历史", "recentSearches": "最近搜索", "popularSearches": "热门搜索", "searchSuggestions": "搜索建议", "quickFilters": "快速筛选", "advancedFilters": "高级筛选", "sortBy": "排序方式", "filterBy": "筛选方式", "showFilters": "显示筛选", "hideFilters": "隐藏筛选", "applyFilters": "应用筛选", "resetFilters": "重置筛选", "tabs": {"all": "全部", "characters": "角色", "stories": "故事", "users": "用户", "memories": "记忆", "creators": "创作者"}, "filters": {"all": "全部", "recent": "最近", "popular": "热门", "trending": "趋势", "newest": "最新", "oldest": "最旧", "relevance": "相关性", "rating": "评分", "followers": "粉丝数", "interactions": "互动数", "verified": "已认证", "official": "官方", "featured": "精选", "premium": "高级", "free": "免费", "category": "分类", "genre": "类型", "tags": "标签", "dateRange": "日期范围", "today": "今天", "thisWeek": "本周", "thisMonth": "本月", "lastMonth": "上个月", "thisYear": "今年", "custom": "自定义范围"}, "categories": {"all": "全部分类", "romance": "浪漫", "adventure": "冒险", "fantasy": "奇幻", "scifi": "科幻", "mystery": "悬疑", "horror": "恐怖", "comedy": "喜剧", "drama": "戏剧", "slice_of_life": "日常生活", "historical": "历史", "anime": "动漫", "games": "游戏", "books": "书籍", "movies": "电影", "original": "原创"}, "characters": {"title": "角色", "subtitle": "找到 {{count}} 个角色", "noCharacters": "未找到角色", "loadMore": "加载更多角色", "viewProfile": "查看资料", "startChat": "开始聊天", "follow": "关注", "following": "已关注", "followers": "{{count}} 个粉丝", "chats": "{{count}} 次聊天", "rating": "{{rating}} 评分", "created": "创建于 {{date}}", "updated": "更新于 {{date}}", "by": "由 {{creator}} 创建", "official": "官方", "verified": "已认证", "premium": "高级", "new": "新", "trending": "趋势", "filterByGender": "按性别筛选", "filterByCategory": "按分类筛选", "filterByTags": "按标签筛选", "male": "男性", "female": "女性", "other": "其他", "notSpecified": "未指定"}, "stories": {"title": "故事", "subtitle": "找到 {{count}} 个故事", "noStories": "未找到故事", "loadMore": "加载更多故事", "viewStory": "查看故事", "startReading": "开始阅读", "chapters": "{{count}} 章", "duration": "{{duration}} 分钟阅读", "likes": "{{count}} 个赞", "reads": "{{count}} 次阅读", "created": "创建于 {{date}}", "updated": "更新于 {{date}}", "by": "由 {{creator}} 创建", "character": "角色：{{name}}", "completed": "已完成", "ongoing": "连载中", "draft": "草稿", "filterByStatus": "按状态筛选", "filterByDuration": "按时长筛选", "short": "短篇（< 30 分钟）", "medium": "中篇（30-60 分钟）", "long": "长篇（> 60 分钟）"}, "users": {"title": "用户", "subtitle": "找到 {{count}} 个用户", "noUsers": "未找到用户", "loadMore": "加载更多用户", "viewProfile": "查看资料", "follow": "关注", "following": "已关注", "followers": "{{count}} 个粉丝", "characters": "{{count}} 个角色", "stories": "{{count}} 个故事", "joined": "加入于 {{date}}", "lastActive": "最后活跃 {{date}}", "creator": "创作者", "verified": "已认证", "premium": "高级会员", "filterByType": "按类型筛选", "filterByActivity": "按活跃度筛选", "allUsers": "全部用户", "creators": "创作者", "premiumUsers": "高级用户", "activeUsers": "活跃用户", "newUsers": "新用户"}, "memories": {"title": "记忆", "subtitle": "找到 {{count}} 个记忆", "noMemories": "未找到记忆", "loadMore": "加载更多记忆", "viewMemory": "查看记忆", "character": "角色：{{name}}", "created": "创建于 {{date}}", "emotion": "情感：{{emotion}}", "importance": "重要性：{{score}}/10", "tags": "标签：{{tags}}", "private": "私密", "shared": "共享", "filterByCharacter": "按角色筛选", "filterByEmotion": "按情感筛选", "filterByImportance": "按重要性筛选", "emotions": {"happy": "快乐", "sad": "悲伤", "excited": "兴奋", "calm": "平静", "romantic": "浪漫", "nostalgic": "怀旧", "surprised": "惊讶", "thoughtful": "深思", "grateful": "感激", "peaceful": "宁静"}}, "recommendations": {"title": "推荐", "subtitle": "发现新内容", "forYou": "为你推荐", "trending": "正在流行", "popular": "本周热门", "featured": "精选内容", "newReleases": "新发布", "becauseYouLiked": "因为你喜欢 {{item}}", "similarTo": "与 {{item}} 相似", "basedOnHistory": "基于你的历史", "exploreMore": "探索更多", "viewAll": "查看全部", "refresh": "刷新推荐", "why": "为什么推荐这个？", "hideRecommendation": "隐藏", "notInterested": "不感兴趣", "reportContent": "举报内容", "easy": "简单", "medium": "中等", "hard": "困难", "premium": "高级"}, "errors": {"searchFailed": "搜索失败，请重试。", "loadFailed": "加载结果失败，请重试。", "networkError": "网络错误，请检查连接。", "serverError": "服务器错误，请稍后重试。", "noPermission": "你没有权限访问此内容。", "contentNotFound": "内容未找到或已被删除。"}, "actions": {"search": "搜索", "filter": "筛选", "sort": "排序", "share": "分享", "save": "保存", "bookmark": "收藏", "report": "举报", "block": "屏蔽", "follow": "关注", "unfollow": "取消关注", "like": "点赞", "unlike": "取消点赞", "view": "查看", "chat": "聊天", "read": "阅读", "play": "播放", "download": "下载", "copy": "复制", "edit": "编辑", "delete": "删除", "refresh": "刷新", "loadMore": "加载更多", "viewMore": "查看更多", "showLess": "显示更少", "expand": "展开", "collapse": "折叠", "close": "关闭", "cancel": "取消", "confirm": "确认", "ok": "确定", "yes": "是", "no": "否", "retry": "重试", "back": "返回", "next": "下一步", "previous": "上一步", "skip": "跳过", "finish": "完成", "done": "完成", "reset": "重置", "clear": "清除", "apply": "应用"}}, "characterEdit": {"title": "编辑角色", "subtitle": "修改角色的详细信息和设置", "saveChanges": "保存更改", "savingChanges": "正在保存更改...", "characterUpdated": "角色更新成功", "navigation": {"previous": "上一步", "next": "下一步", "finish": "保存更改", "stepOf": "第 {{current}} 步，共 {{total}} 步", "stepComplete": "步骤已完成", "stepIncomplete": "请完成必填字段"}}, "characterCreation": {"title": "创建角色", "subtitle": "用AI驱动的角色创建功能让你的想象力成为现实", "functions": {"flash": {"title": "快速生成", "description": "60秒AI智能生成角色", "subtitle": "60秒AI生成角色"}, "customize": {"title": "自定义", "description": "详细设置角色，完全自主控制"}, "import": {"title": "文件导入", "description": "从SillyTavern卡片或外部文件导入"}}, "steps": {"basics": {"title": "基础信息", "description": "姓名、性别、图像、外观、性格、标签"}, "personality": {"title": "行为与知识", "description": "行为、知识、背景、可见性"}, "advanced": {"title": "高级设置", "description": "快速开始或高级故事章节"}}, "flash": {"characterName": "角色姓名", "characterConcept": "角色概念", "namePlaceholder": "输入角色姓名（可选 - AI会自动生成）", "conceptPlaceholder": "描述您的角色：外观、性格、行为、知识和背景故事。我们将为您生成其余细节...", "generateButton": "🚀 生成角色", "generatingText": "正在生成魔法...", "aiFeatures": "✨ AI将自动生成这些方面：", "previewTitle": "✨ 生成的角色预览", "regenerationsLeft": "剩余重新生成次数：", "nameTag": "✨ 姓名", "appearanceTag": "👤 外观", "personalityTag": "🎭 性格", "behaviorTag": "🎪 行为", "knowledgeTag": "🧠 知识", "storyTag": "📖 故事"}, "filters": {"gender": "性别", "pointOfView": "视角", "any": "任意", "female": "女性", "male": "男性", "other": "其他", "femalePOV": "女性视角", "malePOV": "男性视角", "otherPOV": "其他视角"}, "import": {"title": "导入角色", "subtitle": "上传SillyTavern卡片或脚本文件来创建角色", "sillyTavern": "📄 SillyTavern角色卡", "uploadArea": {"title": "点击或拖拽上传", "subtitle": "支持SillyTavern .json文件", "button": "📄 选择JSON文件", "fileSelected": "文件已选择，点击选择其他文件", "readyToImport": "✅ 准备导入"}, "features": {"title": "📋 导入功能", "characterCards": "📄 角色卡", "autoExtract": "自动提取角色数据", "fillDescription": "填充描述和背景"}, "selectCharacter": {"title": "🎯 选择角色", "subtitle": "🎭 选择要导入的角色（找到{{count}}个）"}, "scriptFiles": {"title": "📝 脚本文件", "uploaded": "脚本上传成功", "uploadTitle": "上传脚本文件", "uploadSubtitle": "拖拽或点击上传.txt文件（最大16KB）"}, "importFeatures": {"title": "📋 导入功能", "characterCards": "📄 角色卡", "multipleFiles": "一次多个.json文件", "singleFileMultiple": "单个.json文件包含多个角色", "sillyTavernSupport": "SillyTavern格式支持", "txtSupport": ".txt文件支持（最大16KB）", "autoExtract": "自动提取角色数据", "fillBackground": "填充描述和背景"}, "filters": {"gender": "性别", "pointOfView": "视角", "any": "任意", "female": "女性", "male": "男性", "other": "其他", "femalePOV": "女性视角", "malePOV": "男性视角", "otherPOV": "其他视角"}}, "basics": {"title": "角色基础", "subtitle": "设置您角色的基本信息", "characterName": "角色姓名", "namePlaceholder": "输入角色姓名...", "nameOriginPlaceholder": "输入姓名的来源或含义（可选）", "genderPov": "性别与视角", "appearance": "外观", "appearancePlaceholder": "体格和面部特征，服装和装饰品...", "personality": "性格", "traits": "特质", "traitsPlaceholder": "描述角色特质：勇敢、善良、固执、好奇、忠诚、顽皮、完美主义、冒险、内向、乐观...", "mind": "思维", "mindPlaceholder": "描述思维模式：分析性、创造性、逻辑性、直觉性、战略性、冲动性、有条不紊、抽象性、实用性、哲学性...", "emotion": "情感", "emotionPlaceholder": "描述情感模式：共情、热情、冷静、敏感、表达性、内敛、温暖、强烈、温和、戏剧性、稳定...", "characterSettings": "角色设定", "characterSettingsPlaceholder": "描述角色的背景设定、身份、职业或特殊情况...", "additionalTags": "附加标签", "tagPlaceholder": "输入标签（例如：友好、神秘、战士）", "addTag": "添加标签"}, "personality": {"title": "行为与知识", "subtitle": "定义您角色的行为模式和知识库", "behaviour": "行为", "defaultGreeting": "默认问候语", "greetingPlaceholder": "输入用户开始聊天时角色说的第一句话...", "speechStyle": "说话风格", "speechStylePlaceholder": "描述您的角色说话风格和语调...", "facialExpressions": "面部表情", "facialExpressionsPlaceholder": "描述您的角色面部表情和习惯...", "bodyLanguage": "肢体语言", "bodyLanguagePlaceholder": "描述您的角色肢体语言和手势...", "knowledge": "知识", "knowledgeBase": "知识库", "knowledgePlaceholder": "输入您角色应该了解的一般知识、事实或背景信息...", "uploadKnowledgeFiles": "上传知识文件", "uploadKnowledgeDesc": "支持 .json、.md、.txt 格式文件", "uploadKnowledgeButton": "点击上传知识文件", "uploadedFiles": "已上传的文件：", "deleteFile": "删除文件", "goodAt": "擅长", "goodAtPlaceholder": "例如：烹饪、数学、剑术、魔法咒语、领导力...", "badAt": "不擅长", "badAtPlaceholder": "例如：撒谎、技术、游泳、公开演讲、记住名字...", "add": "添加", "backgroundStory": "背景故事", "relationshipWithPlayer": "与玩家的关系", "relationshipPlaceholder": "描述角色与玩家的关系背景...", "importantExperiences": "重要过往经历", "experiencesPlaceholder": "描述角色的重要经历和背景故事...", "visibility": "可见性", "public": "公开", "publicDesc": "所有人都可以查看和聊天", "unlisted": "未列出", "unlistedDesc": "仅通过直接链接访问", "private": "私人", "privateDesc": "仅您可以访问"}, "advanced": {"title": "高级设置", "subtitle": "快速开始或高级故事章节", "readyToChat": "准备开始聊天了吗？", "readyToChatDesc": "您的角色已经准备好了！点击下方开始对话。", "readyToChatButton": "现在就开始聊天", "advancedStoryBuilding": "高级故事构建", "advancedStoryDesc": "创建详细的故事章节和自定义高级设置。", "createStoryButton": "创建故事", "finishCreatingCharacter": "请先完成角色创建", "checkPassButton": "检查通行证和高级设置", "passVerified": "通行证已验证", "passVerifiedDesc": "检测到活跃通行证或钻石通行证。您现在可以构建详细的故事章节并使用高级功能。", "noActivePass": "没有活跃的通行证", "noActivePassDesc": "高级功能需要活跃的通行证。请升级以访问故事构建工具。", "uploadScript": "上传脚本进行情节分析", "uploadScriptButton": "上传脚本文件", "uploadScriptDesc": "支持 .txt、.md、.json 格式的脚本文件", "storyChapters": "故事章节和角色问候语", "updatedGreeting": "更新的问候语", "greetingAfterChapter": "章节完成后的问候语", "greetingAfterPlaceholder": "输入完成特定章节后的新问候语...", "triggerAfterChapter": "在章节后触发", "chapter": "章节", "addChapter": "添加章节", "addGreeting": "添加问候语"}, "imageUpload": {"characterImages": "角色图片", "characterPortrait": "角色肖像", "avatarAutoCropped": "头像（自动裁剪）", "uploadCharacterImage": "上传角色图片", "clickOrDragToUpload": "点击或拖拽上传", "characterImageUploaded": "角色图片已上传", "clickToChangeImage": "点击更换图片", "avatarReady": "头像已就绪", "croppedFromCharacterImage": "从角色图片裁剪", "uploadCharacterImageFirst": "请先上传角色图片", "avatarWillBeAutoCropped": "头像将自动裁剪", "avatarPreview": "头像预览：", "avatarSetupComplete": "✅ 头像设置完成", "sourceCroppedFromCharacterImage": "来源：从角色图片裁剪"}, "cropper": {"avatar": {"title": "裁剪头像", "character": "裁剪立绘", "description": "拖拽选择框来调整{{type}}区域，比例：{{ratio}}", "selectAspectRatio": "选择比例", "avatarPreview": "头像预览", "characterPreview": "立绘预览", "avatarInfo": "头像信息", "characterRatio": "立绘比例", "dragToMove": "拖拽移动裁剪区域", "dragCorners": "拖拽角落调整大小", "mouseWheel": "滚轮缩放裁剪区域", "resetArea": "重置区域", "resizing": "调整大小", "dragToMoveText": "拖拽移动", "cancel": "取消", "confirmCrop": "确认裁剪", "ratios": {"5:6": "经典人像比例", "5:8": "全身人像比例"}}}, "personalityTags": {"addCustomTag": "添加自定义标签", "customTagPlaceholder": "输入性格特征...", "addButton": "添加", "addTagHint": "按回车键或点击添加按钮来添加自定义标签", "presetTags": {"gentle": "温和", "lively": "活泼", "mysterious": "神秘", "calm": "冷静", "humorous": "幽默", "serious": "严肃", "innocent": "天真", "mature": "成熟", "cheerful": "开朗", "introverted": "内向", "curious": "好奇", "kind": "善良", "brave": "勇敢", "wise": "智慧", "strong": "坚强", "elegant": "优雅", "cute": "可爱", "stern": "严厉", "optimistic": "乐观", "pessimistic": "悲观", "romantic": "浪漫", "realistic": "现实", "rational": "理性", "emotional": "感性"}}, "navigation": {"previous": "上一步", "next": "下一步", "stepOf": "第{{current}}步，共{{total}}步", "finish": "创建角色", "stepComplete": "此步骤已完成", "stepIncomplete": "请完成所有必填字段以继续"}}, "storyCreation": {"pageTitle": "创建故事 - <PERSON><PERSON>", "pageDescription": "为您的角色创建详细的故事情节和章节，丰富互动体验", "title": "创建故事", "subtitle": "为您的角色创建引人入胜的故事体验", "selectCharacter": {"title": "创建您的故事", "subtitle": "选择一个角色开始您的冒险", "searchPlaceholder": "按名称或描述搜索角色...", "selectYourCharacter": "选择您的角色", "viewMode": "视图：", "filters": "筛选器", "storyStatus": "故事状态", "allCharacters": "所有角色", "withStories": "有故事", "withoutStories": "无故事", "gender": "性别", "allGenders": "所有性别", "male": "男性", "female": "女性", "other": "其他", "pointOfView": "视角", "allPOV": "所有视角", "firstPerson": "第一人称", "secondPerson": "第二人称", "thirdPerson": "第三人称", "filterByTags": "按标签筛选", "addTagPlaceholder": "添加标签筛选...", "add": "添加", "charactersFound": "找到 {{count}} 个角色", "characterFound": "找到 {{count}} 个角色", "sortBy": "排序：", "name": "名称", "fans": "粉丝", "heatScore": "热度", "trend": "趋势", "lastUpdated": "最后更新", "totalStories": "总故事数", "totalLikes": "总点赞数", "totalPlays": "总播放数", "noMatchingCharacters": "没有匹配的角色", "noMatchingCharactersDesc": "尝试调整您的搜索或筛选条件，找到适合您故事的完美角色。", "clearAllFilters": "清除所有筛选器", "noCharactersYet": "还没有角色", "noCharactersYetDesc": "创建您的第一个AI角色，开始构建精彩的故事和体验。", "createFirstCharacter": "创建您的第一个角色", "noDescriptionAvailable": "暂无描述", "createStory": "创建故事"}, "success": {"storyCreated": "故事创建成功！"}, "error": {"failedToGenerate": "故事生成失败，请重试", "failedToCreate": "故事创建失败，请重试", "characterNotFound": "角色未找到", "characterNotFoundDescription": "您要查找的角色不存在或已被删除。", "backToCharacterSelection": "返回角色选择"}, "functions": {"flash": {"title": "闪电生成", "description": "AI快速故事生成", "placeholder": "请用一句话描述您的故事概念...", "generate": "生成故事", "generating": "正在生成故事...", "example": "示例：在古罗马的浪漫冒险", "needInspiration": "需要灵感？试试这些示例：", "helpText": "💡 AI将生成包含场景设定、角色心理和互动动态的完整故事"}, "customize": {"title": "自定义", "description": "详细故事自定义", "steps": {"basics": "故事封面", "details": "设定主题", "worldSetting": "世界设定", "storyFlow": "故事流程", "objectivesSubjectives": "目标与主观设定"}}}, "worldSetting": {"title": "世界设定", "description": "配置您故事的世界和环境设定", "basicInfo": {"title": "基本信息", "storyName": "故事名称", "storyNamePlaceholder": "输入您的故事名称...", "storyDescription": "故事简介", "storyDescriptionPlaceholder": "为您的故事提供一个简短的对外展示描述...", "openingMessage": "开场白", "openingMessagePlaceholder": "输入用户开始此故事时将看到的开场消息...", "storyTags": "故事标签", "tagPlaceholder": "输入标签（例如：浪漫、冒险、悬疑）", "addTag": "添加标签", "removeTag": "移除", "coverImage": "封面图片", "uploadCoverImage": "上传封面图片", "coverImageUploaded": "封面图片已上传", "clickToChangeImage": "点击更换图片", "clickOrDragToUpload": "点击或拖拽上传"}, "quickSetup": {"title": "快速设置", "worldOverview": "世界观概述", "worldOverviewPlaceholder": "描述您故事世界的整体设定...", "storyBackground": "故事背景", "storyBackgroundPlaceholder": "描述故事的背景情况..."}, "basicSettings": {"title": "基础设置", "historicalEra": "历史时代", "historicalEraPlaceholder": "例如：维多利亚时代、黄金二十年代、2077年、星际探索时代...", "geographicEnvironment": "地理环境", "geographicEnvironmentPlaceholder": "例如：超级都市、边境沙漠、海底城市、浮空岛屿...", "mainRaces": "主要种族", "mainRacesPlaceholder": "描述世界中的主要种族和文明...", "coreConflict": "核心冲突", "coreConflictPlaceholder": "选择核心冲突...", "storyMainline": "故事主线", "storyMainlinePlaceholder": "描述故事的主要情节线索...", "storyMainlinePlaceholderNew": "例如：拯救被封印的女神、阻止世界毁灭的阴谋、寻找失落的记忆碎片、重建破碎的王国、化解种族间的千年仇恨...", "coreObjective": "核心任务目标", "coreConflictOptions": {"classOpposition": "阶级对立", "ideologicalStruggle": "意识形态斗争", "racialWar": "种族战争", "humanNatureConflict": "人与自然", "resourceScarcity": "资源稀缺"}}, "advancedSettings": {"title": "高级设置", "optional": "可选，让你的世界更丰富", "coreWorldRules": "核心世界规则", "socialEconomicSettings": "社会经济设定", "socialPoliticalSystem": "社会政治制度", "socialFormPlusPolitical": "社会形态 + 政治结构", "socialPoliticalSystemPlaceholder": "例如：后资本主义的联邦共和制，数字游牧联盟结合了无政府主义自治和AI辅助的民主决策，个人通过贡献算法获得投票权重...", "customOption": "自定义...", "physicsRulesCustomPlaceholder": "描述你的自定义物理规则，例如：重力可以被意念控制，时间在某些区域会倒流...", "techLevelCustomPlaceholder": "描述你的自定义科技水平，例如：生物技术高度发达但电子技术停滞...", "supernaturalElementsNewPlaceholder": "例如：高等魔法与蒸汽朋克科技共存，古老神祇沉睡但影响着世界运转，灵魂可以通过特殊仪式转移到机械载体中...", "timeBackgroundNewPlaceholder": "例如：第三次世界大战结束50年后的重建期，新兴科技与传统价值观的冲突达到顶峰...", "economicFoundationNewPlaceholder": "例如：基于时间货币的经济体系，知识产权成为主要财富，机器人劳动使传统就业概念消失...", "showAdvanced": "显示高级设置", "hideAdvanced": "隐藏高级设置", "worldBackgroundSettings": "世界背景设定", "physicsAndRulesSettings": "物理与规则设定", "physicsRules": "物理法则", "physicsRulesPlaceholder": "选择物理法则...", "physicsRulesOptions": {"realistic": "现实物理", "softScifi": "软科幻物理", "highFantasy": "高幻想魔法", "cosmicHorror": "宇宙恐怖未知"}, "supernaturalElements": "超自然元素", "supernaturalElementsPlaceholder": "选择超自然元素...", "supernaturalElementsOptions": {"magicExists": "魔法存在", "godsExist": "神明存在", "otherworldlyBeings": "异界存在", "soulReincarnation": "灵魂轮回", "noSupernatural": "无超自然力量"}, "socialForm": "社会形态", "socialFormPlaceholder": "选择社会形态...", "socialFormOptions": {"capitalism": "资本主义", "postCapitalism": "后资本主义", "cyberpunkFeudalism": "赛博朋克封建主义", "tribalAlliance": "部落联盟", "anarchistFederation": "无政府主义联邦"}, "politicalStructure": "政治结构", "politicalStructurePlaceholder": "选择政治结构...", "politicalStructureOptions": {"federation": "联邦制", "empire": "帝国制", "republic": "共和制", "monarchy": "君主制", "theocracy": "神权制"}, "economicFoundation": "经济基础", "economicFoundationPlaceholder": "描述经济体系...", "techLevel": "科技水平", "techLevelPlaceholder": "选择科技水平...", "techLevelOptions": {"industrialRevolution": "工业革命", "informationAge": "信息时代", "cyberpunkNearFuture": "赛博朋克近未来", "interstellarCivilization": "星际文明", "magicTechFusion": "魔法科技融合"}, "timeBackground": "时代背景", "timeBackgroundPlaceholder": "选择时代背景...", "timeBackgroundOptions": {"postWarReconstruction": "战后重建", "techExplosionEve": "科技爆发前夜", "doomsdayCountdown": "末日倒计时", "goldenAge": "黄金时代", "greatDepression": "大萧条时期"}}}, "storyFlow": {"title": "故事流程", "description": "创建和组织故事章节", "chapters": {"title": "章节管理", "addMainChapter": "添加主章节", "addBranchChapter": "添加分支章节", "deleteChapter": "删除章节", "removeChapter": "删除章节", "chapterTitle": "章节标题", "chapterDescription": "章节描述", "chapterContent": "章节内容", "backgroundSetting": "背景设定", "backgroundSettingPlaceholder": "描述此章节的设定和氛围...", "backgroundImage": "背景图片", "uploadBackgroundImage": "上传背景图片", "backgroundImageUploaded": "背景图片已上传", "clickToChangeImage": "点击更换图片", "clickOrDragToUpload": "点击或拖拽上传", "useWorldSettingImage": "使用世界设定图片", "usingWorldSettingImage": "使用世界设定图片", "inheritedFromWorldSetting": "从世界设定继承", "completionEffects": "完成效果", "bondPointsChange": "好感度变化", "bondPointsPlaceholder": "输入好感度变化 (例如：+10, -5, 0)", "greetingChange": "新问候消息", "greetingChangePlaceholder": "完成此章节后的新问候语...", "characterMoodChange": "角色心情变化", "characterMoodChangePlaceholder": "角色心情如何变化？", "customEffects": "其他效果", "customEffectsPlaceholder": "任何其他效果或变化...", "mainChapter": "主章节", "branchChapter": "分支章节", "noChapters": "还没有章节", "createFirstChapter": "创建第一个章节", "addFirstChapter": "添加第一个章节", "selectChapter": "选择章节", "selectChapterDescription": "从故事流程中选择一个章节来编辑其详细信息", "choices": "章节选择", "addChoice": "添加选择", "choiceText": "选择文本", "choiceDescription": "选择描述", "choiceTriggerCondition": "触发条件", "choiceTriggerConditionPlaceholder": "例如：好感度 ≥ 3，已完成前一个任务...", "nextChapter": "下一章节", "selectNextChapter": "选择下一章节...", "flowPreview": "流程预览"}, "navigation": {"previous": "上一步", "next": "下一步", "continueToObjectives": "继续到目标设定", "backToWorldSetting": "返回世界设定"}}, "objectivesSubjectives": {"title": "目标与主观设定", "description": "配置章节目标和角色心理", "tabs": {"objectives": "客观要素", "subjectives": "主观要素"}, "objectives": {"scene": {"title": "场景层", "environment": "环境", "environmentPlaceholder": "描述场景环境...", "timeElements": {"title": "时间要素", "season": "季节", "seasonPlaceholder": "选择季节...", "timeOfDay": "时间段", "timeOfDayPlaceholder": "选择时间段...", "duration": "持续时间", "durationPlaceholder": "例如：30分钟、2小时..."}, "spatialElements": {"title": "空间要素", "location": "位置", "locationPlaceholder": "描述具体位置...", "atmosphere": "氛围", "atmospherePlaceholder": "描述场景氛围..."}, "environmentalElements": {"title": "环境要素", "weather": "天气", "weatherPlaceholder": "描述天气状况...", "lighting": "光线", "lightingPlaceholder": "描述光线条件...", "sounds": "声音", "soundsPlaceholder": "描述环境声音..."}}, "antecedent": {"title": "前情层", "macroHistory": "宏观历史", "macroHistoryPlaceholder": "描述大背景历史...", "characterPast": "角色过往", "characterPastPlaceholder": "描述角色的过往经历...", "immediateTrigger": "即时触发", "immediateTriggerPlaceholder": "描述触发当前场景的事件..."}}, "subjectives": {"character": {"title": "角色层", "mentalModel": {"title": "心理模型", "coreValues": "核心价值观", "coreValuesPlaceholder": "描述角色的核心价值观...", "thinkingMode": "思维模式", "thinkingModePlaceholder": "描述角色的思维方式..."}, "emotionalBaseline": {"title": "情感基线", "displayedEmotion": "表现情感", "displayedEmotionPlaceholder": "角色表现出的情感...", "hiddenEmotion": "隐藏情感", "hiddenEmotionPlaceholder": "角色内心的真实情感...", "emotionalIntensity": "情感强度", "emotionalIntensityPlaceholder": "0-100的情感强度..."}}, "interaction": {"title": "互动层", "dialogueStrategy": {"title": "对话策略", "initiative": "主动性", "initiativePlaceholder": "0-100的主动程度...", "questioningStyle": "提问风格", "questioningStylePlaceholder": "描述提问方式..."}, "relationshipDynamics": {"title": "关系动态", "initialGoodwill": "初始好感", "initialGoodwillPlaceholder": "0-100的好感度...", "intimacyLevel": "亲密程度", "intimacyLevelPlaceholder": "描述关系亲密程度..."}, "goalOrientation": {"title": "目标导向", "sceneGoal": "场景目标", "sceneGoalPlaceholder": "这个场景要达成什么目标...", "displayedIntent": "表现意图", "displayedIntentPlaceholder": "角色表现出的意图...", "hiddenIntent": "隐藏意图", "hiddenIntentPlaceholder": "角色的真实意图..."}}}}, "characterInfo": {"creator": "创作者", "followers": "关注者", "chats": "聊天次数", "weeklyRank": "周排名", "stories": "故事数", "satisfaction": "满意度", "rating": "评分", "defaultDescription": "一个充满活力和好奇心的角色，满怀热情和善意。"}, "flashGenerator": {"title": "AI故事生成器", "subtitle": "为 {{characterName}} 创建沉浸式故事体验", "features": {"sceneSettings": "🎬 场景设定", "backgroundContext": "📚 背景情境", "characterPsychology": "🧠 角色心理", "dialogueDynamics": "💬 对话动态", "relationshipBuilding": "❤️ 关系建立", "storyGoals": "🎯 故事目标"}, "preview": {"title": "✨ 生成故事预览", "regenerationsLeft": "剩余重新生成次数：{{count}}"}, "chapters": {"title": "📖 故事章节", "sceneInfo": "场景信息", "psychology": "心理状态", "relationships": "关系动态", "goals": "目标设定", "chapterTitle": "第 {{index}} 章", "acceptButton": "✅ 接受并创建故事", "regenerateButton": {"text": "🔄 重新生成（剩余 {{count}} 次）", "generating": "重新生成中..."}}, "sceneSetting": {"title": "场景设定", "environment": "环境", "location": "位置", "time": "时间", "weather": "天气"}, "characterPsychology": {"title": "角色心理", "coreValues": "核心价值观", "displayedEmotion": "表现情感", "hiddenEmotion": "隐藏情感", "thinkingMode": "思维模式"}, "interactionDynamics": {"title": "互动动态", "sceneGoal": "场景目标", "intimacyLevel": "亲密程度", "initiative": "主动性", "goodwill": "好感度"}, "backgroundContext": {"title": "背景情境", "immediateTrigger": "即时触发", "characterPast": "角色过往"}, "actions": {"accept": "接受此故事", "regenerate": "重新生成", "regenerating": "重新生成中...", "copy": "复制", "copied": "已复制"}, "copy": {"chapterTitle": "第 {{index}} 章标题", "chapterTitleTooltip": "复制第 {{index}} 章标题"}, "customizeHint": "💡 您仍可以在自定义部分编辑任何详细信息", "copySuccess": "已复制 {{text}}！"}, "steps": {"worldSetting": {"title": "世界设定", "description": "定义您故事世界的基础"}, "storyFlow": {"title": "故事流程", "description": "创建和组织故事章节", "chapterTitle": "章节标题", "chapterTitlePlaceholder": "输入章节标题", "chapterDescription": "章节描述", "chapterDescriptionPlaceholder": "简要描述此章节", "chapterContent": "章节内容", "chapterContentPlaceholder": "章节内容和叙述", "backgroundSetting": "背景设定", "backgroundSettingPlaceholder": "描述此章节的设定和氛围...", "backgroundImage": "背景图片", "uploadBackgroundImage": "上传背景图片", "backgroundImageUploaded": "背景图片已上传", "clickToChangeImage": "点击更换图片", "clickOrDragToUpload": "点击或拖拽上传", "useWorldSettingImage": "使用世界设定图片", "usingWorldSettingImage": "使用世界设定图片", "inheritedFromWorldSetting": "从世界设定继承", "completionEffects": "完成效果", "bondPointsChange": "好感度变化", "greetingChange": "新问候消息", "greetingChangePlaceholder": "完成此章节后的新问候语...", "characterMoodChange": "角色心情变化", "characterMoodChangePlaceholder": "角色心情如何变化？", "customEffects": "其他效果", "customEffectsPlaceholder": "任何其他效果或变化...", "selectChapter": "选择章节", "selectChapterDescription": "从故事流程中选择一个章节来编辑其详细信息", "storyFlow": "故事流程"}, "objectivesSubjectives": {"title": "目标与主观设定", "description": "配置章节目标和角色心理", "selectChapter": "选择章节", "selectChapterDescription": "从故事流程中选择一个章节来配置其目标和主观设定", "navigation": {"backToStoryFlow": "返回故事流程"}}, "objectives": {"title": "客观要素", "scene": {"title": "场景层", "timeElements": "时间要素", "season": "季节", "selectSeason": "选择季节...", "seasons": {"spring": "春季", "summer": "夏季", "autumn": "秋季", "winter": "冬季"}, "timeOfDay": "时间段", "selectTimeOfDay": "选择时间段...", "timesOfDay": {"dawn": "黎明", "morning": "上午", "noon": "中午", "afternoon": "下午", "evening": "傍晚", "night": "夜晚", "midnight": "午夜"}, "duration": "持续时间", "durationPlaceholder": "例如：30分钟、2小时、一整天...", "specialDate": "特殊日期", "specialDatePlaceholder": "如果有特殊意义的日期...", "spatialElements": "空间要素", "location": "位置", "locationPlaceholder": "描述具体位置...", "atmosphere": "氛围", "atmospherePlaceholder": "描述场景氛围...", "environmentalElements": "环境要素", "weather": {"label": "天气", "selectWeather": "选择天气...", "sunny": "晴朗", "cloudy": "多云", "rainy": "雨天", "stormy": "暴风雨", "snowy": "雪天", "foggy": "雾天"}, "lighting": {"label": "光线", "selectLighting": "选择光线...", "bright": "明亮", "dim": "昏暗", "dark": "黑暗", "candlelit": "烛光", "neon": "霓虹灯"}}, "antecedent": {"title": "前情层", "macroHistory": "宏观历史", "macroHistoryPlaceholder": "描述大背景历史...", "characterPast": "角色过往", "characterPastPlaceholder": "描述角色的过往经历...", "immediateTrigger": "即时触发", "immediateTriggerPlaceholder": "描述触发当前场景的事件..."}}, "subjectives": {"title": "主观要素", "character": {"title": "角色层", "mentalModel": "心理模型", "coreValues": "核心价值观", "coreValuesPlaceholder": "描述角色的核心价值观...", "thinkingMode": "思维模式", "thinkingModePlaceholder": "描述角色的思维方式...", "decisionLogic": "决策逻辑", "decisionLogicPlaceholder": "描述角色如何做决定...", "emotionalBaseline": "情感基线", "displayedEmotion": "表现情感", "displayedEmotionPlaceholder": "角色表现出的情感...", "hiddenEmotion": "隐藏情感", "hiddenEmotionPlaceholder": "角色内心的真实情感...", "emotionalIntensity": "情感强度", "emotionalStability": "情感稳定性"}, "interaction": {"title": "互动层", "dialogueStrategy": "对话策略", "communicationStyle": "沟通风格", "communicationStylePlaceholder": "描述角色的沟通方式...", "responsePattern": "回应模式", "responsePatternPlaceholder": "描述角色的回应习惯...", "goalOrientation": "目标导向", "primaryGoal": "主要目标", "primaryGoalPlaceholder": "这个场景中角色的主要目标...", "conflictResolution": "冲突解决", "conflictResolutionPlaceholder": "角色如何处理冲突..."}}, "chapters": {"chapterTitle": "章节标题"}}, "buttons": {"createStory": "创建故事"}, "navigation": {"stepOf": "第 {{current}} 步，共 {{total}} 步"}, "validation": {"atLeastOneChapter": "至少需要一个章节才能创建故事"}}, "characterProfile": {"header": {"followers": "关注者", "chats": "聊天", "weeklyRank": "周排名", "interactions": "互动", "memories": "回忆", "relationshipLevel": "关系等级", "creatorLabel": "创作者"}, "description": {"default": "Aiko 是一个充满活力和好奇心的女孩，充满童真和热情。她喜欢探索未知，结交新朋友，用她的乐观和善良激励身边的每一个人。"}, "stats": {"todaysRewards": "今日奖励", "interactionStreak": "互动连击", "dailyTasks": "每日任务", "completed": "已完成", "days": "天", "closeFriend": "亲密朋友", "relationship": "关系"}, "actions": {"startChat": "开始聊天", "follow": "关注"}, "tabs": {"memories": "回忆", "stories": "故事", "badges": "徽章"}, "badges": {"daily": "每日", "weekly": "每周", "monthly": "每月", "totalChats": "聊天总数", "interactionTime": "互动时间", "intimacyGrowth": "亲密度增长", "memoriesCreated": "创建的回忆"}, "stories": {"official": "官方", "community": "社区", "chapters": "章节", "moments": "瞬间", "completed": "已完成", "ongoing": "连载中", "reads": "阅读量"}}, "profile": {"redirect": {"message": "正在跳转到您的个人资料..."}, "header": {"followers": "关注者", "following": "关注中", "moments": "动态", "memories": "回忆", "liked": "点赞", "links": "链接"}, "description": {"default": "热衷于创造沉浸式AI角色和引人入胜的故事。构建想象力与技术相遇的世界。"}, "actions": {"follow": "关注", "message": "私信"}, "tabs": {"moments": "动态", "likes": "点赞", "friends": "朋友"}, "errors": {"userNotFound": "用户未找到", "userNotFoundDesc": "您要找的用户不存在。"}, "anonymous": "匿名用户"}, "creatorProfile": {"redirect": {"message": "正在跳转到您的创作者资料..."}, "header": {"creatorBadge": "👑 创作者", "followers": "关注者", "following": "关注中", "characters": "角色", "stories": "故事", "earnings": "收益", "links": "链接"}, "description": {"default": "热衷于创造沉浸式AI角色和引人入胜的故事。构建想象力与技术相遇的世界。"}, "actions": {"follow": "关注", "message": "私信"}, "tabs": {"characters": "角色", "stories": "故事", "dashboard": "仪表板"}, "dashboard": {"totalEarnings": "总收益", "followers": "关注者", "totalLikes": "总点赞", "thisMonth": "本月", "topCharacters": "热门角色", "onlyVisibleToCreator": "仪表板仅创作者可见。"}, "stories": {"published": "已发布", "draft": "草稿", "reads": "阅读量", "chapters": "章节"}, "errors": {"creatorNotFound": "创作者未找到", "creatorNotFoundDesc": "您要找的创作者不存在。"}, "anonymous": "匿名用户"}, "storyManagement": {"title": "管理故事", "subtitle": "管理您创建的故事，查看分析数据并跟踪表现", "createNew": "创建新故事", "myStories": "我的故事", "searchPlaceholder": "按标题、描述或角色搜索故事...", "stats": {"totalStories": "总故事数", "totalPlays": "总播放量", "totalLikes": "总点赞数", "avgRating": "平均评分"}, "status": {"published": "已发布", "draft": "草稿", "archived": "已归档"}, "difficulty": {"easy": "简单", "normal": "普通", "hard": "困难"}, "filter": {"all": "全部状态", "published": "已发布", "draft": "草稿", "archived": "已归档"}, "table": {"story": "故事", "status": "状态", "plays": "播放量", "likes": "点赞数", "rating": "评分", "actions": "操作"}, "actions": {"view": "查看故事", "edit": "编辑故事"}, "noStories": "还没有故事", "noStoriesDescription": "您还没有创建任何故事。创建您的第一个故事来开始构建互动体验。", "createFirstStory": "创建第一个故事", "noMatches": "没有匹配的故事", "noMatchesDescription": "没有故事匹配您的搜索条件。尝试不同的关键词或清除搜索。"}, "storyEdit": {"pageTitle": "编辑故事 - <PERSON><PERSON>", "pageDescription": "编辑和更新您的故事详情、章节和设置", "title": "编辑您的故事", "subtitle": "更新和完善您的叙事体验，为", "success": {"storyUpdated": "故事更新成功！"}, "error": {"failedToUpdate": "更新故事失败，请重试", "failedToLoad": "加载故事数据失败，请重试", "storyNotFound": "未找到故事", "storyNotFoundDescription": "您尝试编辑的故事不存在或已被删除。", "backToStoryManagement": "返回故事管理"}}, "trophies": {"title": "成就系统", "subtitle": "展示您的成就并解锁专属奖励", "tabs": {"overview": "概览", "overviewDesc": "奖杯总览", "achievements": "成就", "achievementsDesc": "聊天深度与质量", "explorations": "探索", "explorationsDesc": "发现与广度", "social": "社交", "socialDesc": "社区与分享", "ranking": "排名", "rankingDesc": "竞争与排行"}, "categories": {"all": "全部", "beginner": "新手", "interaction": "互动", "creation": "创作", "collection": "收集", "social": "社交", "special": "特殊事件"}, "rarity": {"bronze": "青铜", "silver": "白银", "gold": "黄金", "platinum": "铂金", "diamond": "钻石", "legendary": "传奇"}, "status": {"locked": "锁定", "inProgress": "进行中", "completed": "已完成", "claimed": "已领取"}, "filters": {"showAll": "显示全部", "showCompleted": "仅已完成", "showInProgress": "仅进行中", "showLocked": "仅锁定", "sortBy": "排序方式", "sortRarity": "稀有度", "sortProgress": "进度", "sortDate": "获得日期", "sortAlphabetical": "字母顺序", "status": "状态", "rarity": "稀有度", "allRarities": "所有稀有度", "activeFilters": "当前筛选：", "clearAll": "清除全部", "category": "分类：", "search": "搜索：", "achievements": {"all": "全部", "allDesc": "所有成就类型", "conversation": "对话", "conversationDesc": "基础聊天互动", "depth": "深度", "depthDesc": "深度对话成就", "quality": "质量", "qualityDesc": "高质量互动奖励", "consistency": "一致性", "consistencyDesc": "定期参与奖励"}, "explorations": {"all": "全部", "allDesc": "所有探索类型", "characters": "角色", "charactersDesc": "角色相关发现", "creation": "创作", "creationDesc": "内容创作成就", "discovery": "发现", "discoveryDesc": "新发现奖励", "collection": "收集", "collectionDesc": "收集和完成目标"}, "social": {"all": "全部", "allDesc": "所有社交活动", "community": "社区", "communityDesc": "社区参与", "sharing": "分享", "sharingDesc": "内容分享成就", "friendship": "友谊", "friendshipDesc": "建立友谊", "support": "支持", "supportDesc": "帮助他人"}, "ranking": {"all": "全部", "allDesc": "所有排名成就", "competitive": "竞技", "competitiveDesc": "竞技成就", "elite": "精英", "eliteDesc": "精英玩家奖励", "legendary": "传奇", "legendaryDesc": "传奇成就", "seasonal": "赛季", "seasonalDesc": "赛季竞赛"}}, "stats": {"completionRate": "完成率", "overallProgress": "总体进度"}, "overview": {"title": "奖杯概览", "subtitle": "您在所有类别中的成就之旅", "totalTrophies": "总奖杯数", "totalPoints": "总积分", "earnedFromAll": "来自所有分类", "recentAchievements": "最近成就", "latestUnlocked": "最新解锁的奖杯", "of": "共", "unlocked": "已解锁", "points": "积分", "achievements": "成就", "achievementsSubtitle": "聊天深度与质量", "achievementsDesc": "深度对话和有意义的互动", "explorations": "探索", "explorationsSubtitle": "发现与广度", "explorationsDesc": "角色发现和内容探索", "social": "社交", "socialSubtitle": "社区与分享", "socialDesc": "社区参与和分享", "ranking": "排名", "rankingSubtitle": "竞争与排行", "rankingDesc": "竞技成就和排行榜"}, "leaderboard": {"title": "全球排行榜", "topPlayers": "本赛季顶级玩家", "rank": "排名", "player": "玩家", "trophies": "奖杯", "change": "变化"}, "emptyStates": {"achievements": {"title": "未找到成就", "description": "尝试调整筛选条件或开始对话来解锁成就。"}, "explorations": {"title": "未找到探索", "description": "尝试调整筛选条件或开始探索来解锁成就。"}, "social": {"title": "未找到社交成就", "description": "尝试调整筛选条件或开始参与社区活动来解锁成就。"}, "ranking": {"title": "未找到排名成就", "description": "尝试调整筛选条件或参与挑战来解锁成就。"}}, "common": {"progress": "进度", "of": "共", "points": "积分", "pointsEarned": "获得积分", "claimReward": "领取奖励", "close": "关闭", "completed": "已完成", "inProgress": "进行中", "total": "总计", "discovered": "已发现", "exploring": "探索中", "achievements": "个成就", "achievement": "个成就", "allAchievements": "全部成就", "allExplorations": "全部探索"}, "achievementsTab": {"title": "成就", "subtitle": "掌握深度对话和有意义连接的艺术", "conversationMaster": {"title": "对话大师", "description": "进行有意义的对话，与AI角色建立持久的连接。", "messagesSent": "发送消息", "conversations": "对话次数"}, "emotionalDepth": {"title": "情感深度", "description": "与角色创造深层情感纽带和有意义的回忆。", "memoryCapsules": "记忆胶囊", "bondLevel": "羁绊等级"}, "consistencyStreak": {"title": "连续记录", "description": "保持定期参与并建立持久习惯。", "currentStreak": "当前连续", "bestStreak": "最佳连续"}}, "explorationsTab": {"title": "探索", "subtitle": "发现新世界、角色和隐藏宝藏", "characterExplorer": {"title": "角色探索者", "description": "遇见多样化的角色，探索平台上不同的个性。", "charactersMet": "遇见角色"}, "creativeExplorer": {"title": "创意探索者", "description": "创建并发布您自己的角色和故事供他人发现。", "createdCharacters": "创建角色", "publishedStories": "发布故事"}, "memoryCollector": {"title": "记忆收集者", "description": "收集您冒险中的珍贵记忆和时刻。", "memoryCapsules": "记忆胶囊"}, "discoveryMap": {"title": "发现地图", "worldsExplored": "探索世界", "secretsFound": "发现秘密", "storiesDiscovered": "发现故事", "charactersMet": "遇见角色"}}, "socialTab": {"title": "社交", "subtitle": "连接、分享，在社区中建立有意义的关系", "connected": "已连接", "building": "建设中", "communityBuilder": {"title": "社区建设者", "description": "与其他用户连接，在社区中建立持久的友谊。", "friends": "朋友", "followers": "关注者"}, "contentSharer": {"title": "内容分享者", "description": "与社区分享您最喜欢的角色和时刻。", "shares": "分享", "likesReceived": "收到点赞"}, "communityHelper": {"title": "社区助手", "description": "帮助新手并支持其他社区成员。", "helpfulVotes": "有用投票", "guidesWritten": "编写指南"}, "socialActivity": {"title": "社交活动", "receivedLikes": "您的角色分享收到了15个点赞", "newFollowers": "3个新关注者加入了您的社区", "helpedNewcomer": "帮助新手进行角色创建", "hoursAgo": "2小时前", "dayAgo": "1天前", "daysAgo": "3天前"}, "communityStats": {"comments": "评论", "shares": "分享", "likesGiven": "给出点赞", "featuredPosts": "精选帖子"}, "allSocialAchievements": "全部社交成就"}, "rankingTab": {"title": "排名", "subtitle": "为荣耀而战，在精英中占据一席之地", "conquered": "已征服", "competing": "竞争中", "globalRank": {"title": "全球排名", "outOf": "共10,247名玩家", "champion": "冠军"}, "seasonRank": {"title": "赛季排名", "season": "2024-1赛季", "elite": "精英"}, "powerLevel": {"title": "力量等级", "description": "总成就力量", "legendary": "传奇"}, "competitiveStats": {"competitionsWon": "赢得竞赛", "titlesEarned": "获得头衔", "daysAtTop": "第一名天数", "winStreak": "连胜记录"}, "leaderboard": {"points": "积分"}, "allRankingAchievements": "全部排名成就"}, "card": {"progress": "进度", "reward": "奖励", "claimReward": "领取奖励", "viewDetails": "查看详情", "requirement": "要求", "earned": "获得时间", "points": "积分"}, "modal": {"achievementDetails": "成就详情", "description": "描述", "requirements": "要求", "rewards": "奖励", "progress": "您的进度", "earnedDate": "获得于", "tips": "提示", "relatedAchievements": "相关成就", "close": "关闭"}, "rewards": {"badge": "专属徽章", "title": "特殊头衔", "currency": "货币奖励", "item": "特殊物品", "privilege": "平台特权", "experience": "经验点数"}, "achievements": {"firstSteps": {"name": "初试啼声", "description": "完成与AI角色的第一次对话", "requirement": "在单次对话中发送10条消息", "tips": "开始与任何角色聊天来开启您的旅程！"}, "conversationalist": {"name": "健谈者", "description": "掌握引人入胜的对话艺术", "requirement": "完成100次对话", "tips": "探索不同的角色和话题来提升您的社交技能"}, "memoryKeeper": {"name": "记忆守护者", "description": "珍贵时刻的守护者", "requirement": "创建50个记忆胶囊", "tips": "将有意义的对话保存为记忆胶囊"}, "creator": {"name": "角色创造者", "description": "让想象力变为现实", "requirement": "创建您的第一个角色", "tips": "使用角色创建工具设计独特的个性"}, "socialite": {"name": "社交达人", "description": "在社区中建立联系", "requirement": "关注20个不同的创作者", "tips": "在社区版块发现优秀的创作者"}, "streakMaster": {"name": "连击大师", "description": "坚持是通往伟大的关键", "requirement": "保持30天的互动连击", "tips": "每天登录并完成至少一次对话"}, "explorer": {"name": "探索者", "description": "无限可能的冒险家", "requirement": "与25个不同的角色聊天", "tips": "尝试不同类型和个性的角色"}, "storyteller": {"name": "故事讲述者", "description": "引人入胜故事的编织者", "requirement": "创建并发布5个故事", "tips": "使用故事创建工具制作引人入胜的叙述"}, "perfectionist": {"name": "完美主义者", "description": "追求每个细节的卓越", "requirement": "您的角色获得100个赞", "tips": "专注于角色质量和独特个性"}, "legendary": {"name": "传奇创造者", "description": "被所有人认可的大师", "requirement": "达到10,000次角色互动", "tips": "创建能够引起社区共鸣的角色"}}, "underConstruction": {"title": "即将推出！", "description": "成就系统正在精心制作中。敬请期待惊人的体验！"}, "empty": {"noAchievements": "未找到成就", "noAchievementsDesc": "尝试调整筛选条件或完成更多活动来解锁成就", "startJourney": "开始您的旅程", "startJourneyDesc": "开始与AI角色聊天来解锁您的第一个成就！", "noMatchingAchievements": "没有匹配的成就", "noMatchingDesc": "我们找不到与您当前筛选条件匹配的成就。请尝试调整搜索条件或清除所有筛选。", "activeFilters": "当前筛选：", "clearAllFilters": "清除所有筛选", "yourJourneyAwaits": "您的奖杯之旅在等待！", "startEngaging": "开始与角色互动并创建内容来解锁您的第一个成就。", "startChatting": "开始聊天", "startChattingDesc": "开始与AI角色对话来解锁您的第一个成就", "createContent": "创建内容", "createContentDesc": "设计角色和故事来获得创作者成就"}, "social": {"leaderboard": {"title": "社交排行榜", "subtitle": "与朋友比较成就并发现他们的专长", "inviteFriends": "邀请朋友", "friendsLeaderboard": "好友排行榜", "you": "您", "level": "等级{{level}}", "recentAchievements": "最近成就", "challengeFriend": "挑战朋友", "viewProfile": "查看档案"}, "stats": {"achievements": "成就", "points": "积分", "qualityScore": "质量分数", "completion": "完成度"}, "mockNames": {"masterStoryteller": "故事大师", "socialButterfly": "社交达人", "memoryKeeper": "记忆守护者", "legendaryCreator": "传奇创造者"}}, "stories": {"timeline": {"title": "成就故事", "subtitle": "您的成就之旅以视觉故事形式呈现", "gallery": "画廊视图", "timeline": "时间线视图", "noStories": "暂无故事", "shareStory": "分享故事", "downloadMoment": "下载时刻", "filterBy": "筛选", "sortBy": "排序", "viewDetails": "查看详情"}}, "community": {"title": "社区中心", "subtitle": "与成就猎人们联系并分享您的进展", "newPost": "新帖子", "activeChallenges": "活跃挑战", "joinChallenge": "参加挑战", "joined": "已参加", "level": "等级", "reply": "回复", "tabs": {"allPosts": "全部帖子", "guides": "指南", "challenges": "挑战", "celebrations": "庆祝"}, "timeUnits": {"hoursAgo": "{{hours}}小时前", "daysLeft": "还剩{{days}}天", "minutesAgo": "{{minutes}}分钟前"}, "actions": {"like": "点赞", "reply": "回复", "share": "分享"}, "badges": {"expertGuide": "专家指南"}, "rewards": {"exclusiveExplorerBadge": "专属探索者徽章"}, "mockContent": {"streakMasterTip": "🔥 连击大师攻略：设定每日提醒并准备备用对话话题！"}, "challenges": {"weekendExplorerRush": "周末探索冲刺", "chatWith10Characters": "本周末与10个不同角色聊天"}}, "personalization": {"settings": "个性化设置", "theme": "主题偏好", "displayMode": "显示模式", "celebrationStyle": "庆祝风格", "focusCategory": "主要关注", "rarityPriority": "稀有度优先级", "previewMode": "预览模式", "resetDefaults": "重置为默认", "exportSettings": "导出设置"}, "views": {"dashboard": "仪表盘", "grid": "网格", "social": "社交", "stories": "故事", "community": "社区"}, "dashboard": {"aiRecommendations": {"title": "AI 智能推荐", "subtitle": "基于您的进度和偏好的个性化建议", "reasons": {"almostComplete": "就快完成了！再努力一点就能完成这个成就。", "timeSensitive": "时间敏感！这个成就如果不及时完成可能会错过。", "quickWin": "快速胜利！这个成就可以在一小时内完成。"}, "priority": {"high": "高", "medium": "中", "low": "低"}}, "timeSensitive": {"title": "⚠️ 时间敏感成就", "subtitle": "这些成就如果不及时完成可能会变得不可用", "missable": "可错过"}, "activeProgress": {"title": "当前进度", "progress": "进度"}, "readyToClaim": {"title": "🎉 准备领取！", "pointsEarned": "获得{{points}}积分", "claimReward": "领取奖励"}, "common": {"mins": "{{time}}分钟", "pts": "{{points}}积分"}}}, "memory": {"title": "记忆胶囊", "subtitle": "珍藏与AI伙伴的美好时光，让每一个重要时刻都被永远记住", "stats": {"total": "总记忆数", "characters": "涉及角色", "importance": "平均重要性", "references": "AI引用次数"}, "search": {"placeholder": "搜索记忆...试试\"生日\"、\"约定\"、\"梦想\"", "aiPowered": "AI语义搜索"}, "view": {"grid": "网格视图", "timeline": "时间轴视图"}, "filter": {"allCharacters": "所有角色", "allEmotions": "所有情感", "happy": "快乐", "sad": "感动", "excited": "兴奋", "thoughtful": "深思", "important": "重要", "allTime": "所有时间", "today": "今天", "thisWeek": "本周", "thisMonth": "本月", "importance": "重要性筛选", "allImportance": "全部", "tags": "标签筛选", "clearTags": "清除所有标签"}, "time": {"today": "今天", "yesterday": "昨天", "daysAgo": "{{days}}天前"}, "emotion": {"happy": "快乐", "sad": "感动", "excited": "兴奋", "thoughtful": "深思", "important": "重要"}, "action": {"edit": "编辑", "delete": "删除", "save": "保存", "cancel": "取消", "generateArt": "生成记忆画作", "share": "分享", "export": "导出", "confirmDelete": "确认删除", "createFirst": "创建第一个记忆", "learnMore": "了解更多"}, "detail": {"summary": "记忆摘要", "fullDialogue": "完整对话", "importance": "重要性评分", "tags": "标签", "aiInsights": "AI洞察", "referenceCount": "这段记忆已被AI引用{{count}}次，在您与{{character}}的对话中发挥了重要作用。"}, "confirm": {"deleteTitle": "确认删除", "deleteMessage": "确定要删除这条记忆吗？此操作无法撤销。"}, "empty": {"noResults": "没有找到匹配的记忆", "tryDifferent": "试试调整筛选条件或使用不同的搜索关键词", "title": "开始创建你的第一个记忆胶囊", "description": "将与AI伙伴的美好对话永久保存，让每一个重要时刻都被珍藏。记忆胶囊会帮助AI更好地理解和记住你。", "goChat": "去聊天创建记忆", "learnMore": "了解记忆胶囊"}, "feature": {"smart": "智能检索", "smartDesc": "AI自动引用相关记忆", "art": "记忆画作", "artDesc": "将记忆转化为精美画作", "bond": "加深羁绊", "bondDesc": "让AI伙伴更懂你"}}, "store": {"title": "Alphane商店", "subtitle": "通过高级功能和数字珍品提升您的AI伴侣体验", "limited": "限时", "countdown": "倒计时", "categories": {"subscriptions": "会员套餐", "currency": "货币", "characters": "角色", "items": "道具", "memory": "记忆艺术", "featured": "精选", "new": "新品上架", "memberships": "会员", "welcome": "欢迎", "arts": "艺术", "memorial": "纪念日", "mindfuel": "思维燃料"}, "tabs": {"featured": {"label": "精选", "description": "热门精选 & 优惠"}, "memberships": {"label": "会员", "description": "高级套餐"}, "welcome": {"label": "欢迎", "description": "新用户优惠"}, "arts": {"label": "艺术", "description": "角色 & 场景"}, "memorial": {"label": "纪念日", "description": "特别活动"}, "currency": {"label": "货币", "description": "代币 & 积分"}, "mindfuel": {"label": "思维燃料", "description": "思想能量道具"}}, "featured": {"perMonth": "每月", "discount": "25% 折扣", "unlimited": "无限", "premium": "高级", "exclusive": "独家", "creatorBenefits": "创作者福利", "currentPrice": "$29.99", "originalPrice": "$39.99", "unlimitedDescription": "100点燃料容量 + 10倍超高速回复", "premiumDescription": "图像 + 语音多媒体互动", "exclusiveDescription": "AI伴侣日记与耳语空间", "creatorBenefitsDescription": "最高20%收益分成"}, "subscriptions": {"title": "会员套餐", "subtitle": "选择您的完美套餐，享受无限AI体验", "currentPlan": "当前套餐", "upgrade": "升级", "renew": "续费", "cancel": "取消", "manage": "管理订阅", "benefits": "福利", "mostPopular": "最受欢迎", "recommended": "推荐", "monthly": "月", "activeSubscriptions": "活跃订阅", "expires": "到期", "alwaysActive": "永久有效", "active": "有效", "validUntil": "有效期至", "currentlyActive": "当前有效", "cancelAtNextBilling": "下次计费周期取消", "moreFeatures": "更多功能", "ultimate": "至尊", "free": "免费", "explorer": {"name": "Alphane Explorer", "period": "永久", "features": ["完全免费使用", "基础AI模型对话", "10点思维燃料容量", "记忆胶囊容量100个", "14天云端存档保护", "按需付费购买更多记忆胶囊存储空间和思维燃料", "基础社区互动权限"]}, "pass": {"name": "Alphane Pass", "shortName": "Pass", "features": ["包含所有Alphane Explorer功能", "高级AI模型对话", "30点思维燃料容量 + 3倍快速恢复速度", "图片发送功能", "记忆胶囊容量500个", "对话提示享受2折优惠", "会员期间永久数据存储", "角色/故事卡创建权限", "解锁高级每日签到礼品", "双轨道季节奖励", "1张体验卡赠送权限", "Alphane Pass会员专属身份徽章", "购买记忆胶囊拓展包和思维燃料享受9折优惠", "24/7邮件客服支持"]}, "diamond": {"name": "Alphane Diamond", "shortName": "Diamond", "description": "重度用户、内容创作者和商业用户的首选", "features": ["包含所有Alphane Pass功能", "100点思维燃料容量 + 10倍极速恢复速度", "图片+语音互动功能", "记忆胶囊容量2000个", "每日免费对话提示50条", "最高20%创作者收益分成", "三轨道季节奖励", "3张体验卡赠送权限", "Alphane Diamond会员专属身份徽章", "AI伴侣日记和密语空间", "新功能优先体验权", "更多高级角色功能访问权", "购买记忆胶囊拓展包和思维燃料享受8折优惠", "24/7高级客服支持"]}, "infinity": {"name": "Alphane Infinity", "shortName": "Infinity", "description": "专业创作者和顶级玩家的终极选择", "features": ["包含所有Alphane Diamond功能", "无限对话思维燃料", "无限优先响应权限", "无限记忆胶囊容量", "无限免费对话提示", "更高的角色/故事卡创建限额", "最高50%创作者收益分成", "四轨道季节奖励", "8张体验卡赠送权限", "Alphane Infinity会员专属身份徽章", "专属客服代表支持", "专属VIP社群"]}, "alphanePass": {"name": "Alphane Pass", "shortName": "心悦轨道", "description": "日常互动必备的伴侣功能", "price": "$9.99", "period": "月", "features": ["每日快速请求特权（50次）", "每日奖励：50曦光微尘 + 10心悦晶石", "心灵之旅高级奖励轨道", "每月2张连击冻结卡", "创建分享公开角色卡", "会员专属身份徽章", "优先客服支持"]}, "diamondPass": {"name": "Alphane Diamond", "shortName": "钻石轨道", "description": "拥有无限可能的终极AI伴侣体验", "price": "$19.99", "period": "月", "features": ["无限快速请求特权", "增强每日奖励：150微尘 + 50晶石 + 1记忆拼图", "心灵之旅钻石收藏轨道", "每月5张连击冻结卡 + 2张免费修复卡", "创作者激励计划（50%收益分成）", "专属密语空间访问权限", "增强AI记忆胶囊容量（3倍）", "新AI模型和功能优先体验", "专属钻石会员特权", "高级角色创建工具"]}, "limitedTimeOffer": "限时优惠", "upgradeToDiamond": "升级至钻石会员", "instantAccess": "即时访问", "secureBilling": "安全计费", "cancelAnytime": "随时取消", "allFeatures": "全部功能"}, "currency": {"title": "数字货币", "subtitle": "通过高级货币包增强您的互动体验", "yourBalance": "您的余额", "purchaseEndora": "购买Endora", "exchangeCurrencies": "兑换货币", "starEndora": "Star Endora", "purchaseStarEndora": "购买Star Endora", "purchaseDescription": "用美元购买endora - 所有兑换的主要货币", "exchangeTitle": "将Endora兑换为其他货币", "exchangeDescription": "将您的endora转换为不同功能的专门货币", "availableCurrencies": "可用货币", "exchangeForSpecialized": "将endora兑换为专门货币", "purchaseRate": "1美元 = 100 Endora", "recharge": "充值", "purchase": "购买", "bonus": "赠送", "firstTimeBonus": "首次充值奖励", "popular": "热门", "bestValue": "最超值", "currencies": {"starEndora": {"name": "Star Endora", "description": "用真钱购买的高级货币"}, "alphane": {"name": "Alphane", "description": "用于角色互动和特殊能力", "uses": "角色对话，特殊动作", "exchangeRate": "1 Endora = 10 Alphane"}, "serotile": {"name": "Serotile", "description": "用于记忆创建和自定义的特殊代币", "uses": "记忆艺术，场景创建，自定义内容", "exchangeRate": "5 Endora = 1 Serotile"}, "oxytol": {"name": "Oxytol", "description": "深化角色关系的关系货币", "uses": "关系进展，亲密场景，特殊纽带", "exchangeRate": "2 Endora = 1 Oxytol"}}, "packages": {"starterBundle": "新手套装", "popularChoice": "热门选择", "bestValuePack": "最超值套装", "premiumElite": "高级精英", "ultimateTreasure": "终极宝藏", "legendaryVault": "传奇宝库", "starter": {"name": "入门包", "price": "$4.99", "amount": "300", "bonus": "+50"}, "standard": {"name": "标准包", "price": "$9.99", "amount": "680", "bonus": "+120"}, "premium": {"name": "高级包", "price": "$19.99", "amount": "1480", "bonus": "+320"}, "deluxe": {"name": "豪华包", "price": "$49.99", "amount": "3980", "bonus": "+1020"}, "ultimate": {"name": "至尊包", "price": "$99.99", "amount": "8500", "bonus": "+2500"}}, "glimmeringDust": {"name": "曦光微尘", "description": "日常活动和基础互动的必备货币", "uses": "基础物品、礼物兑换、连击冻结卡碎片"}, "joyCrystal": {"name": "心悦晶石", "description": "高级功能和独家内容的优质货币", "uses": "记忆艺术生成、角色高级礼物、场景解锁"}, "memoryPuzzle": {"name": "忆境拼图", "description": "解锁特殊角色故事的珍稀收藏品碎片", "uses": "角色背景故事解锁、独家艺术品、AI记忆升级"}, "bondDew": {"name": "羁绊之露", "description": "深化与AI角色关系的特殊货币", "uses": "角色亲密度等级、独家互动、记忆位升级"}, "starDiamond": {"name": "星钻", "description": "最独家功能和内容的顶级货币", "uses": "高级角色卡、独家故事线、限定版物品"}}, "characters": {"title": "高级角色", "subtitle": "发现由顶级创作者打造的独家AI伴侣", "official": "官方收藏", "creators": "创作者精选", "limited": "限定版", "featured": "精选角色", "new": "新品发布", "rarity": {"common": "普通", "rare": "稀有", "epic": "史诗", "legendary": "传奇", "mythic": "神话"}, "categories": {"romance": "浪漫", "adventure": "冒险", "fantasy": "奇幻", "scifi": "科幻", "slice_of_life": "日常", "mystery": "悬疑"}}, "items": {"title": "高级道具", "subtitle": "通过特殊道具和个性化内容提升您的体验", "themes": {"title": "聊天主题", "description": "为您的对话提供美丽的背景"}, "avatars": {"title": "头像框", "description": "展示您个人资料的专属边框"}, "effects": {"title": "特效", "description": "消息和互动的动画效果"}, "bundles": {"title": "礼包套装", "description": "精选套装，独家道具超值优惠"}, "gacha": {"title": "神秘宝箱", "description": "惊喜盒子，珍稀道具和收藏品"}, "memory": {"title": "记忆工具", "description": "管理AI记忆的高级工具"}}, "memoryArt": {"title": "记忆艺术工坊", "subtitle": "将珍贵回忆转化为美丽的AI生成艺术品", "canvases": {"title": "记忆画布", "description": "用于创作记忆艺术品的特殊画布", "blank": "空白记忆画布", "premium": "高级记忆画布", "legendary": "传奇记忆画布"}, "styles": {"title": "艺术风格包", "description": "为您的记忆创作提供独家艺术风格", "anime": "动漫风格包", "watercolor": "水彩风格包", "oil": "油画风格包", "digital": "数字艺术风格包"}, "workshop": {"title": "记忆艺术工坊", "description": "从您最珍贵的时刻生成令人惊叹的艺术品", "create": "创作记忆艺术", "gallery": "我的画廊"}}, "cart": {"title": "购物车", "empty": "您的购物车为空", "emptyDescription": "浏览我们的商店，为您的AI伴侣体验寻找精彩物品", "continueShopping": "继续购物", "total": "总计", "subtotal": "小计", "tax": "税费", "checkout": "结算", "remove": "移除", "quantity": "数量", "addToCart": "加入购物车", "buyNow": "立即购买"}, "purchase": {"success": "购买成功！", "processing": "正在处理付款...", "failed": "购买失败", "retry": "重试付款", "receipt": "查看收据", "downloadReceipt": "下载收据"}, "promotion": {"limitedTime": "限时优惠", "newUser": "新用户专享", "flashSale": "限时抢购", "weekend": "周末特惠", "seasonal": "季节活动"}, "filters": {"all": "全部物品", "priceRange": "价格范围", "category": "类别", "rarity": "稀有度", "sortBy": "排序方式", "newest": "最新优先", "popular": "最受欢迎", "priceAsc": "价格：从低到高", "priceDesc": "价格：从高到低", "rating": "评分最高"}, "wishlist": {"title": "心愿单", "add": "添加到心愿单", "remove": "从心愿单移除", "empty": "您的心愿单为空", "emptyDescription": "将物品添加到心愿单以跟踪您想要的内容"}, "reviews": {"title": "评价", "rating": "评分", "writeReview": "写评价", "helpful": "有帮助", "verified": "已验证购买"}, "help": {"title": "需要帮助？", "support": "联系支持", "faq": "常见问题", "refund": "退款政策", "security": "支付安全"}, "welcome": {"title": "欢迎优惠", "loadingOffers": "正在加载您的专属优惠...", "limitedTimeSpecials": "限时特惠", "exclusiveRewards": "{{count}} 个专属奖励", "viewDetails": "查看详情", "claimRewards": "领取奖励", "claimNow": "立即领取", "maybeLater": "稍后再说", "claimed": "已领取", "claiming": "领取中...", "version": "版本 {{version}}", "welcomeBack": "欢迎回到 Alphane！", "welcomeBackMessage": "我们想念您！您上次访问是 {{date}}。请在过期前领取您的欢迎回归奖励。", "firstTimePurchase": {"title": "首次购买特惠", "subtitle": "首次购买专享奖励", "doubleBonus": "双倍奖励", "extraCharacter": "额外角色", "vipTrial": "VIP试用", "claim": "领取优惠"}}, "arts": {"categories": {"all": "全部艺术", "allDescription": "浏览所有可用的艺术内容", "profile": "个人艺术", "profileDescription": "个人头像 & 聊天气泡", "character": "角色艺术", "characterDescription": "角色系列 & 收藏集", "story": "故事艺术", "storyDescription": "故事 & 场景艺术作品", "memory": "记忆艺术", "memoryDescription": "自定义记忆创作"}, "rarity": {"common": "普通", "rare": "稀有", "epic": "史诗", "legendary": "传奇"}, "subTypes": {"avatarFrame": "头像框", "chatBubble": "聊天气泡", "characterSeries": "角色系列", "storyScene": "故事场景", "environment": "环境", "memoryArt": "记忆艺术"}, "badges": {"new": "新品", "limited": "限定", "discount": "-{{discount}}%"}, "actions": {"purchase": "购买"}, "workshop": {"title": "艺术创作工坊", "description": "使用我们的AI工具为您的角色和故事创建自定义艺术作品", "createCustomArt": "创建自定义艺术", "viewGallery": "查看画廊"}}, "memorial": {"activeMemorials": "活跃纪念日", "recentlyClaimed": "最近领取", "types": {"first_meeting": "初遇纪念日", "perfect_intimacy": "完美亲密度纪念日", "perfect_storyline": "完美剧情纪念日"}, "timeRemaining": {"expired": "已过期", "hoursLeft": "剩余 {{hours}} 小时", "daysLeft": "剩余 {{days}} 天"}, "characterInfo": "{{characterName}} • {{daysCount}} 天", "actions": {"claimMemorial": "领取纪念日", "processing": "处理中...", "claimed": "已领取"}, "claimRewardsTitle": "领取纪念日奖励", "claimRewardsDescription": "要为{{characterName}}领取纪念日奖励吗？", "cancelButton": "取消", "claimNowButton": "立即领取"}, "memorialDay": {"title": "个人庆典", "limitedTime": "限时活动", "description": "生日 • 友谊里程碑 • 亲密度成就 • 特殊时刻", "celebrationRewards": "庆典奖励：", "claimButton": "领取", "offerExpires": "优惠剩余时间：", "aboutTitle": "关于个人庆典", "aboutDescription": "个人庆典向您与每个角色的独特旅程致敬。从生日和友谊里程碑到亲密度成就和对话记录，每个庆典都提供专属奖励，反映您的特殊纽带。这些限时优惠庆祝您关系的深度！"}, "currencyCard": {"exchangeCurrency": "交换货币", "uses": "用途", "currentBalance": "当前余额", "exchangeRate": "兑换率", "endoraToSpend": "要花费的Endora：", "youllReceive": "您将获得：", "processing": "处理中...", "exchangeNow": "立即兑换", "popularChoice": "热门选择", "bestValue": "超值", "limitedTime": "限时", "totalAmount": "总数量", "totalValue": "总价值", "discount": "-{{discount}}%", "purchase": "购买", "firstTimeBonus": "首次购买奖励", "popular": "热门", "bestValueTag": "超值", "baseEndora": "基础Endora", "bonusEndora": "奖励Endora", "totalEndora": "总计Endora", "moreValue": "{{percentage}}% 超值 • 每$1获得{{endoraPerDollar}}个Endora", "purchaseNow": "立即购买"}, "mindFuelItems": {"title": "思维燃料补给品", "items": {"small": {"name": "小型思维补给", "description": "恢复1点思维燃料"}, "standard": {"name": "标准思维补给", "description": "恢复3点思维燃料"}, "premium": {"name": "高级思维补给", "description": "恢复5点思维燃料"}, "perfect": {"name": "完美思维补给", "description": "完全恢复思维燃料"}}, "rarity": {"common": "普通", "rare": "稀有", "epic": "史诗", "legendary": "传奇"}, "currency": {"alphane": "Alphane", "endora": "Endora"}, "popular": "热门", "discount": "-{{discount}}%", "purchase": "购买", "purchaseSuccess": "购买成功！\n{{name}} x{{quantity}}\n花费：{{price}} {{currency}}"}, "featuredCard": {"tags": {"quarterlySpecial": "季度特惠", "monthlySpecial": "月度特惠", "weeklySpecial": "周度特惠", "ipCollab": "IP联名", "newFeature": "新功能"}, "collaboration": "联名合作：{{name}}", "purchaseLimit": "限购：{{limit}}次", "purchaseLimitPlural": "限购：{{limit}}次", "daysLeft": "剩余{{days}}天", "purchased": "已购买", "limitReached": "已达限购", "remainingPurchases": "剩余{{count}}次", "getWithEndora": "用 Endora 获取", "processing": "处理中...", "confirmPurchase": "确认 Endora 购买", "save": "节省{{percentage}}%", "validFor": "有效期{{days}}天 • 将从您的余额中扣除 Endora", "cancel": "取消", "ok": "确定"}}, "wallet": {"title": "钱包", "subtitle": "管理您的游戏货币和奖励", "gameWallet": "游戏货币钱包", "currencyWallet": "数字货币钱包", "loading": "正在加载钱包数据...", "lastUpdated": "最后更新", "balance": "余额", "overview": "总览", "orders": "订单", "membership": "会员", "mindFuel": "思维燃料", "used24h": "24小时使用", "earned24h": "24小时获得", "spent24h": "24小时消费", "nextRecovery": "下次恢复", "searchOrders": "搜索订单...", "allOrders": "所有订单", "noOrders": "未找到订单", "noOrdersDescription": "您还没有进行任何购买。访问商店开始购买吧！", "currentMembership": "当前会员", "expiresOn": "到期时间", "mindFuelBonus": "思维燃料奖励", "recoverySpeed": "恢复速度", "specialFeatures": "专属功能", "membershipFeatures": "会员特权", "buyCurrency": "购买货币", "buyCurrencyDesc": "购买游戏货币", "upgradeMembership": "升级会员", "upgradeMembershipDesc": "解锁高级权益", "manageMindFuel": "管理思维燃料", "manageMindFuelDesc": "查看使用和恢复情况", "earnRewards": "赚取奖励", "earnRewardsDesc": "完成任务和挑战", "howToEarn": "如何赚取货币", "currencyTips": "货币使用指南", "alphaneDesc": "曦光微尘 (基础活跃代币)", "endoraDesc": "心悦晶石 (进阶活跃与付费代币)", "serotileDesc": "忆境拼图 (收集与探索代币)", "oxytolDesc": "羁绊之露 (角色羁绊成长代币)", "mindFuelDesc": "思维燃料（对话使用的体力）", "alphaneUse": "用于日常任务奖励、聊天加成和基础互动", "endoraUse": "用于高级互动、特殊活动和独家内容", "serotileUse": "收集以解锁角色故事片段和叙事体验", "oxytolUse": "投入深化关系和建立更强的羁绊", "dailyTasks": "每日任务", "dailyTasksDesc": "完成日常活动获得稳定奖励", "chatRewards": "聊天奖励", "chatRewardsDesc": "通过精彩对话赚取货币", "achievements": "成就", "achievementsDesc": "完成各种里程碑解锁奖励", "specialEvents": "特殊活动", "specialEventsDesc": "参与限时活动获得额外奖励"}, "orders": {"title": "订单与交易", "subtitle": "查看您的购买历史和游戏货币交易记录", "loading": "正在加载订单...", "purchaseOrders": "购买订单", "gameTransactions": "游戏交易", "searchPlaceholder": "搜索订单或交易...", "allStatuses": "所有状态", "allTypes": "所有类型", "completed": "已完成", "pending": "待处理", "failed": "失败", "cancelled": "已取消", "current": "当前", "quickActions": "快捷操作", "subscription": "订阅", "currency": "货币", "character": "角色", "item": "道具", "earned": "获得", "spent": "消费", "purchased": "购买", "refunded": "退款", "viewDetails": "查看详情", "downloadReceipt": "下载收据", "noOrders": "没有找到订单", "noOrdersDescription": "您还没有进行任何购买。访问商店开始购买吧！", "noTransactions": "没有找到交易", "noTransactionsDescription": "您的游戏货币交易记录将在您获得和消费货币时显示在这里。"}, "notifications": {"title": "通知中心", "description": "及时了解您的AI伴侣旅程动态，获取个性化通知", "all": "全部", "system": "系统", "social": "社交", "profile": "个人资料", "subscription": "订阅", "unreadCount": "{{count}} 条未读通知", "inThisCategory": "在此类别中", "markAllAsRead": "标记全部为已读", "emptyState": {"title": "暂无{{category}}通知", "all": "暂无通知！您的活动和更新将在此处显示。", "system": "暂无系统通知。系统更新和维护提醒将在此处显示。", "social": "暂无社交通知。新关注者和社区活动将在此处显示。", "profile": "暂无个人资料通知。个人资料更新和变化将在此处显示。", "subscription": "暂无订阅通知。计费和计划更新将在此处显示。"}, "types": {"system": {"systemUpdate": "系统更新", "maintenance": "维护通知", "security": "安全更新", "newFeatures": "新功能", "announcement": "公告"}, "social": {"newFollower": "新关注者", "followBack": "回关", "characterLike": "角色点赞", "storyLike": "故事点赞", "comment": "评论", "mention": "提及", "weeklyFollowSummary": "每周关注摘要"}, "profile": {"profileUpdate": "个人资料更新", "avatarChange": "头像更改", "verification": "身份验证", "achievement": "成就", "levelUp": "等级提升", "statsUpdate": "统计更新"}, "subscription": {"renewal": "订阅续费", "expiry": "到期提醒", "paymentMethod": "付款方式", "upgrade": "升级可用", "downgrade": "降级通知", "billing": "账单"}}, "actions": {"view": "查看", "dismiss": "忽略", "markAsRead": "标记为已读", "markAsUnread": "标记为未读", "delete": "删除", "openLink": "打开链接", "goToProfile": "前往个人资料", "goToSettings": "前往设置", "goToStore": "前往商店"}, "time": {"now": "刚刚", "minutesAgo": "{{minutes}} 分钟前", "hoursAgo": "{{hours}} 小时前", "daysAgo": "{{days}} 天前", "weeksAgo": "{{weeks}} 周前", "monthsAgo": "{{months}} 个月前"}, "messages": {"allRead": "所有通知已标记为已读", "deleted": "通知已删除", "error": "加载通知失败", "noConnection": "网络连接失败", "retry": "重试"}}, "settings": {"title": "设置", "subtitle": "个性化您的AI伴侣体验", "unsavedChanges": "您有未保存的更改", "saveChanges": "保存更改", "discardChanges": "放弃更改", "saving": "正在保存...", "saved": "设置保存成功！", "resetToDefaults": "重置为默认值", "exportData": "导出数据", "categories": {"account": {"title": "账户与安全", "description": "管理您的账户设置和安全偏好", "changePassword": "修改密码", "changePasswordDesc": "更新您的账户密码以保障安全", "linkedEmail": "绑定邮箱", "currentEmail": "当前邮箱：{{email}}", "changeEmail": "更改邮箱", "twoFactorAuth": "两步验证", "twoFactorAuthDesc": "为您的账户增加额外的安全层", "linkedAccounts": "关联账户", "linkedAccountsDesc": "管理第三方账户连接", "deleteAccount": "删除账户", "deleteAccountDesc": "永久删除您的账户和所有数据", "requestDeletion": "申请删除", "ageVerification": "年龄验证", "ageVerificationDesc": "验证您的年龄以访问所有功能", "verified": "已验证", "pending": "待处理", "notVerified": "未验证", "verify": "立即验证"}, "aiInteraction": {"title": "AI交互", "description": "配置您与AI角色的互动方式", "responseSpeed": "响应速度", "responseSpeedDesc": "选择您偏好的AI回复风格", "responseSpeedOptions": {"fast": "快速（简洁回复）", "standard": "标准（平衡）", "detailed": "详细（全面）"}, "emotionalIntensity": "情感表达", "emotionalIntensityDesc": "调整AI情感回应的强度", "emotionalIntensityOptions": {"subtle": "含蓄（轻柔）", "moderate": "适中（自然）", "rich": "丰富（表达力强）"}, "memorySuggestions": "记忆建议", "memorySuggestionsDesc": "AI主动建议保存重要对话时刻", "bondingNotifications": "羁绊通知", "bondingNotificationsDesc": "当亲密度等级提升时显示特殊动画", "memoryCapacity": "记忆容量", "memoryCapacityDesc": "选择您的AI记忆胶囊容量", "memoryCapacityOptions": {"basic": "基础（100条记忆）", "enhanced": "增强（500条记忆）", "premium": "高级（无限制）"}, "preferredModel": "AI模型", "preferredModelDesc": "选择您偏好的AI模型", "preferredModelOptions": {"standard": "标准模型", "gemini_2_5_flash": "Gemini 2.5 Flash", "gemini_2_5_pro": "Gemini 2.5 Pro"}, "autoSaveMemories": "自动保存记忆", "autoSaveMemoriesDesc": "自动保存重要对话时刻", "contextAwareness": "上下文感知", "contextAwarenessDesc": "AI记住对话上下文的能力", "contextAwarenessOptions": {"basic": "基础（最近消息）", "enhanced": "增强（会话上下文）", "deep": "深度（长期上下文）"}, "personalityAdaptation": "个性适应", "personalityAdaptationDesc": "允许AI根据互动调整个性", "voicePreference": "语音偏好", "voicePreferenceDesc": "选择您偏好的互动模式", "voicePreferenceOptions": {"text_only": "仅文本", "voice_enabled": "启用语音", "voice_preferred": "偏好语音"}, "responseLength": "回复长度", "responseLengthDesc": "AI回复的偏好长度", "responseLengthOptions": {"concise": "简洁（简短）", "balanced": "平衡（适中）", "detailed": "详细（全面）"}, "creativityLevel": "创造力水平", "creativityLevelDesc": "AI回复的创造力程度", "creativityLevelOptions": {"conservative": "保守（可预测）", "balanced": "平衡（适中）", "creative": "创造性（富有想象力）"}}, "privacy": {"title": "隐私与权限", "description": "控制您的隐私和数据分享设置", "profileVisibility": "个人资料可见性", "profileVisibilityDesc": "选择谁可以看到您的个人资料信息", "profileVisibilityOptions": {"public": "公开（所有人）", "followers_only": "仅关注者", "private": "私密（仅自己）"}, "memorySharing": "记忆分享", "memorySharingDesc": "控制您的AI记忆如何被使用", "memorySharingOptions": {"disabled": "禁用", "anonymous": "匿名（不含个人信息）", "full": "完全分享"}, "digitalTwinInteraction": "数字孪生互动", "digitalTwinInteractionDesc": "控制谁可以与您的数字孪生互动", "digitalTwinInteractionOptions": {"anyone": "任何人", "followers_only": "仅关注者", "disabled": "禁用"}, "characterAttribution": "角色归属", "characterAttributionDesc": "在创建的角色上显示您的真实用户名", "dataCollection": "数据收集", "dataCollectionDesc": "允许收集数据以改善服务", "analyticsOptIn": "分析统计", "analyticsOptInDesc": "通过分享使用分析来帮助改善服务", "shareUsageData": "分享使用数据", "shareUsageDataDesc": "分享匿名使用数据以帮助改善服务", "allowPersonalization": "允许个性化", "allowPersonalizationDesc": "允许AI根据您的偏好个性化回复", "cookiePreferences": "<PERSON><PERSON>偏好", "cookiePreferencesDesc": "管理您的Cookie和跟踪偏好", "searchIndexing": "搜索引擎索引", "searchIndexingDesc": "允许搜索引擎索引您的公开内容", "socialMediaSharing": "社交媒体分享", "socialMediaSharingDesc": "允许分享内容到社交媒体平台", "locationTracking": "位置追踪", "locationTrackingDesc": "允许基于位置的功能和内容"}, "notifications": {"title": "通知", "description": "管理您的通知偏好", "streakReminders": "连击提醒", "streakRemindersDesc": "提醒您保持每日互动连击", "battlePassProgress": "战令进度", "battlePassProgressDesc": "关于战令等级提升和奖励的通知", "newCharacterReleases": "新角色发布", "newCharacterReleasesDesc": "新官方角色发布时获得通知", "followedCharacterUpdates": "关注角色更新", "followedCharacterUpdatesDesc": "来自您关注角色的更新", "promotionalOffers": "促销优惠", "promotionalOffersDesc": "接收关于销售和特殊活动的通知", "doNotDisturb": "免打扰", "doNotDisturbDesc": "设置您不希望接收通知的安静时段", "doNotDisturbStart": "开始时间", "doNotDisturbEnd": "结束时间", "pushNotifications": "推送通知", "pushNotificationsDesc": "在您的设备上接收通知", "emailNotifications": "邮件通知", "emailNotificationsDesc": "通过邮件接收通知", "inAppNotifications": "应用内通知", "inAppNotificationsDesc": "在应用内显示通知", "weeklyDigest": "每周摘要", "weeklyDigestDesc": "接收您活动的每周摘要", "maintenanceNotifications": "维护通知", "maintenanceNotificationsDesc": "接收系统维护通知", "friendActivityNotifications": "好友活动", "friendActivityNotificationsDesc": "好友互动的通知", "achievementNotifications": "成就通知", "achievementNotificationsDesc": "解锁成就时接收通知", "memoryMilestones": "记忆里程碑", "memoryMilestonesDesc": "庆祝特殊的记忆时刻", "bondingLevelUps": "羁绊等级提升", "bondingLevelUpsDesc": "亲密度等级提升时的通知", "taskReminders": "任务提醒", "taskRemindersDesc": "提醒您未完成的日常任务", "premiumExpiryReminders": "会员到期提醒", "premiumExpiryRemindersDesc": "订阅到期提醒", "notSet": "未设置", "setTime": "设置时间"}, "display": {"title": "显示与内容", "description": "自定义您的应用外观和内容偏好", "language": "界面语言", "languageDesc": "选择您偏好的语言", "languageOptions": {"en": "English", "zh": "简体中文", "ja": "日本語"}, "theme": "主题", "themeDesc": "选择您偏好的配色方案", "themeOptions": {"auto": "自动（跟随系统）", "light": "浅色模式", "dark": "深色模式"}, "fontSize": "字体大小", "fontSizeDesc": "调整文本大小以获得更好的阅读体验", "fontSizeOptions": {"small": "小（12px）", "medium": "中（14px）", "large": "大（16px）", "extra_large": "特大（18px）"}, "chatBackground": "聊天背景", "chatBackgroundDesc": "自定义您的聊天界面外观", "customizeBackgrounds": "自定义背景", "animationLevel": "动画等级", "animationLevelDesc": "控制界面动画和过渡效果", "animationLevelOptions": {"none": "无", "reduced": "简化", "standard": "标准", "rich": "丰富"}, "contentFilter": "内容过滤", "contentFilterDesc": "启用不当内容过滤", "regionalization": "本地化", "regionalizationDesc": "将内容适配到您的地区和文化", "highContrast": "高对比度", "highContrastDesc": "增加对比度以提高可见性", "reducedMotion": "减少动效", "reducedMotionDesc": "减少动画和过渡效果", "customCssEnabled": "自定义CSS", "customCssEnabledDesc": "允许自定义CSS样式（高级功能）", "chatBubbleStyle": "聊天气泡样式", "chatBubbleStyleDesc": "选择您偏好的聊天气泡外观", "chatBubbleStyleOptions": {"rounded": "圆润（友好）", "square": "方形（简洁）", "minimal": "极简（简单）"}, "messageTimestamps": "消息时间戳", "messageTimestampsDesc": "在消息上显示时间戳", "compactMode": "紧凑模式", "compactModeDesc": "使用更紧凑的界面布局", "showTypingIndicators": "输入指示器", "showTypingIndicatorsDesc": "显示AI正在输入回复的指示"}, "gamification": {"title": "游戏化", "description": "自定义您的游戏体验和成就", "achievementAnimations": "成就动画", "achievementAnimationsDesc": "解锁成就时显示庆祝动画", "currencyGainNotifications": "货币通知", "currencyGainNotificationsDesc": "获得货币时显示浮动通知", "taskReminderIntensity": "任务提醒强度", "taskReminderIntensityDesc": "控制每日任务提醒的频率", "taskReminderIntensityOptions": {"low": "低", "moderate": "适中", "high": "高"}, "memoryArtStyle": "记忆艺术风格", "memoryArtStyleDesc": "记忆艺术品生成的默认艺术风格", "memoryArtStyleOptions": {"anime": "动漫风格", "realistic": "写实风格", "abstract": "抽象风格", "custom": "自定义风格"}, "streakMotivation": "连击激励", "streakMotivationDesc": "接收关于您连击的鼓励消息", "progressCelebrations": "进度庆祝", "progressCelebrationsDesc": "达到里程碑时显示特殊效果", "competitiveMode": "竞争模式", "competitiveModeDesc": "启用竞争功能和排行榜", "leaderboardVisibility": "排行榜可见性", "leaderboardVisibilityDesc": "控制您在排行榜上的可见性", "leaderboardVisibilityOptions": {"public": "公开（所有人）", "friends": "仅好友", "private": "私密（隐藏）"}, "autoClaimRewards": "自动领取奖励", "autoClaimRewardsDesc": "自动领取可用奖励", "experienceDisplayMode": "经验显示", "experienceDisplayModeDesc": "如何显示经验和进度", "experienceDisplayModeOptions": {"detailed": "详细（显示数字）", "simplified": "简化（进度条）", "minimal": "极简（仅里程碑）"}, "badgeDisplayMode": "徽章显示", "badgeDisplayModeDesc": "如何显示您的成就徽章", "badgeDisplayModeOptions": {"all": "所有徽章", "favorites": "仅收藏", "recent": "仅最近"}}, "dataManagement": {"title": "数据管理", "description": "管理您的数据存储和隐私", "clearCache": "清除缓存", "clearCacheDesc": "删除临时文件以释放空间", "clearNow": "立即清除", "exportPersonalData": "导出个人数据", "exportPersonalDataDesc": "下载您的聊天记录和记忆胶囊", "requestExport": "申请导出", "dataUsageStats": "数据使用统计", "dataUsageStatsDesc": "查看您的平台使用数据和统计信息", "viewStats": "查看统计", "autoMemoryBackup": "自动记忆备份", "autoMemoryBackupDesc": "自动将您的AI记忆备份到云端", "autoCleanup": "自动清理", "autoCleanupDesc": "自动删除旧的临时文件", "dataRetentionPeriod": "数据保留", "dataRetentionPeriodDesc": "保留您的数据多长时间（天）", "cacheSize": "缓存大小", "cacheSizeDesc": "当前缓存大小：{{size}}MB", "backupFrequency": "备份频率", "backupFrequencyDesc": "备份您的数据的频率", "backupFrequencyOptions": {"daily": "每日", "weekly": "每周", "monthly": "每月"}, "storageOptimization": "存储优化", "storageOptimizationDesc": "通过压缩旧数据来优化存储", "compressionEnabled": "数据压缩", "compressionEnabledDesc": "压缩数据以节省存储空间", "cloudSyncEnabled": "云同步", "cloudSyncEnabledDesc": "在设备间同步您的数据", "localStorageLimit": "本地存储限制", "localStorageLimitDesc": "最大本地存储（MB）", "downloadHistory": "下载历史", "downloadHistoryDesc": "记录您的数据下载历史", "chatHistoryLimit": "聊天历史限制", "chatHistoryLimitDesc": "保留的最大聊天消息数量"}, "premium": {"title": "高级功能", "description": "钻石通行证会员专属功能", "creatorTools": "创作者工具", "creatorToolsDesc": "访问高级角色创建和分析工具", "advancedAnalytics": "高级分析", "advancedAnalyticsDesc": "您的角色表现的详细洞察", "prioritySupport": "优先支持", "prioritySupportDesc": "从我们的支持团队获得更快的响应时间", "exclusiveContent": "独家内容", "exclusiveContentDesc": "访问高级角色和故事线", "whisperSpaceAccess": "密语空间访问", "whisperSpaceAccessDesc": "加入独家高级会员讨论", "unlimitedFastRequests": "无限快速请求", "unlimitedFastRequestsDesc": "快速AI响应请求无限制", "enhancedMemoryCapacity": "增强记忆容量", "enhancedMemoryCapacityDesc": "3倍大的记忆胶囊容量", "whisperSpaceSettings": "密语空间设置", "whisperSpaceSettingsDesc": "管理您的高级社交特权", "configureSettings": "配置设置", "customUIThemes": "自定义UI主题", "customUIThemesDesc": "访问独家界面主题", "advancedFilters": "高级过滤器", "advancedFiltersDesc": "更复杂的内容过滤选项", "betaFeatureAccess": "Beta功能访问", "betaFeatureAccessDesc": "抢先体验实验性功能", "aiModelSelection": "AI模型选择", "aiModelSelectionDesc": "从多个AI模型中选择", "customPersonalities": "自定义个性", "customPersonalitiesDesc": "创建和使用自定义AI个性"}}, "actions": {"save": "保存", "cancel": "取消", "reset": "重置", "export": "导出", "import": "导入", "delete": "删除", "change": "更改", "configure": "配置", "manage": "管理", "upgrade": "升级", "enable": "启用", "disable": "禁用"}, "messages": {"settingsSaved": "设置保存成功！", "settingsReset": "设置已重置为默认值", "exportRequested": "数据导出请求已发送。准备好后您将收到邮件通知。", "cacheCleared": "缓存清除成功", "confirmReset": "确定要将所有设置重置为默认值吗？", "confirmDelete": "确定要删除您的账户吗？此操作无法撤销。", "confirmClearCache": "清除缓存以释放存储空间？", "upgradeRequired": "此功能需要钻石通行证会员资格", "featureComingSoon": "此功能即将推出！", "errorSaving": "保存设置时出错。请重试。", "errorLoading": "设置加载失败，请刷新页面。"}}, "auth": {"loginTitle": "登录 Alphane.ai", "registerTitle": "创建你的 Alphane.ai 账号", "needAccount": "需要账号？", "alreadyHaveAccount": "已有账号？", "byContinuing": "继续即表示你同意我们的", "terms": "服务条款", "and": "和", "privacy": "隐私政策", "password": "密码", "smsEmailCode": "短信/邮箱验证码", "emailAddress": "邮箱地址", "verificationCode": "验证码", "resend": "重新发送", "getCode": "获取验证码", "rememberMe": "记住我", "forgotPassword": "忘记密码？", "signingIn": "登录中...", "continue": "继续", "or": "或", "continueWithGoogle": "使用 Google 继续", "dontHaveAccount": "还没有账号？", "signUp": "注册", "verificationCodeOtp": "验证码 (OTP)", "confirmPassword": "确认密码", "iAgree": "我同意", "creatingAccount": "创建账户中...", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "请输入密码", "otpPlaceholder": "6位验证码", "passwordMinPlaceholder": "至少8个字符", "passwordRepeatPlaceholder": "重复密码", "errors": {"emailRequired": "邮箱为必填项", "emailInvalid": "请输入有效的邮箱地址", "passwordRequired": "密码为必填项", "verificationCodeRequired": "验证码为必填项", "verificationCodeLength": "验证码必须为6位数字", "passwordMinLength": "密码至少需要8个字符", "confirmPassword": "请确认你的密码", "passwordsNotMatch": "两次输入的密码不一致", "agreeTerms": "你必须同意服务条款和隐私政策"}}, "story": {"title": "故事详情", "header": {"back": "返回", "continueStory": "继续故事"}, "info": {"starring": "由", "starring2": "主演", "estimatedDuration": "预计游戏时长：", "creator": "创建者:", "plays": "次体验", "likes": "次点赞", "rating": "评分", "progress": "故事进度", "chapters": "章节", "completed": "完成", "current": "当前"}, "actions": {"like": "点赞", "share": "分享", "favorite": "收藏"}, "sections": {"description": "故事简介", "tags": "故事标签", "openingMessage": "开场白", "chapterProgress": "章节进度", "keyChoices": "关键选择", "completionRewards": "完成奖励", "unlockableAchievements": "可解锁成就", "participatingCharacters": "参与角色", "unlockConditions": "解锁条件"}, "chapters": {"title": "章节进度", "enterChapter": "进入章节:", "completed": "已完成", "current": "当前", "locked": "锁定"}, "choices": {"title": "关键选择", "description": "在第{{chapter}}章中，你将面临重要的选择分支：", "result": "结果:"}, "rewards": {"moreRewards": "还有 {{count}} 个奖励..."}, "achievements": {"title": "可解锁成就"}, "characters": {"title": "参与角色", "mainRole": "主角", "supportingRole": "配角", "minorRole": "次要角色", "mainDescription": "，活泼开朗", "supportingDescription": "，故事关键人物", "bondLevel": "羁绊 Lv.{{level}}", "unlocked": "已解锁", "locked": "未解锁"}, "unlock": {"title": "解锁条件", "bondLevel": "与{{character}}羁绊等级达到{{level}}级", "tutorialComplete": "完成新手引导", "monthlyPass": "拥有月卡特权（推荐）"}, "currency": {"fire": "🔥", "diamond": "💎", "puzzle": "🧩", "drop": "💧"}}}